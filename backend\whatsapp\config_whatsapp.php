<?php
/**
 * Configuración de WhatsApp - MarcSoftware Solutions
 * Configuraciones centralizadas para la integración de WhatsApp
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

// Configuración de números de teléfono
// ⚠️ IMPORTANTE: Cambia estos números por los tuyos reales
define('WHATSAPP_SOPORTE', '18095551234');        // 🔄 CAMBIAR: Tu número principal de soporte
define('WHATSAPP_VENTAS', '18095551235');         // 🔄 CAMBIAR: Tu número de ventas
define('WHATSAPP_FACTURACION', '18095551236');    // 🔄 CAMBIAR: Tu número para facturación

// Ejemplo de configuración real:
// define('WHATSAPP_SOPORTE', '18091234567');        // Tu número real
// define('WHATSAPP_VENTAS', '18091234567');         // Puede ser el mismo
// define('WHATSAPP_FACTURACION', '18091234567');    // Puede ser el mismo

// Configuración de códigos de país
define('WHATSAPP_CODIGO_PAIS_DEFAULT', '1809');   // República Dominicana

// Configuración de mensajes predeterminados
define('WHATSAPP_MENSAJE_SOPORTE', 'Hola, necesito soporte técnico de MarcSoftware Solutions');
define('WHATSAPP_MENSAJE_VENTAS', 'Hola, me interesa conocer más sobre los servicios de MarcSoftware Solutions');
define('WHATSAPP_MENSAJE_FACTURACION', 'Hola, tengo una consulta sobre mi factura');

// Configuración de horarios de atención
define('WHATSAPP_HORARIO_INICIO', '08:00');
define('WHATSAPP_HORARIO_FIN', '18:00');
define('WHATSAPP_DIAS_ATENCION', 'Lunes a Viernes');

// Configuración de plantillas de mensajes
$WHATSAPP_PLANTILLAS = [
    'factura_nueva' => [
        'titulo' => 'Nueva Factura Generada',
        'emoji' => '🧾',
        'template' => "🧾 *NUEVA FACTURA GENERADA*\n\n📄 *Factura:* {numero}\n👤 *Cliente:* {cliente}\n💰 *Monto:* {moneda} {precio}\n📅 *Vence:* {fecha_vencimiento}\n💳 *Pago:* {modo_pago}\n\n{datos_bancarios}Gracias por elegir MarcSoftware Solutions 🚀"
    ],
    
    'recordatorio_pago' => [
        'titulo' => 'Recordatorio de Pago',
        'emoji' => '🔔',
        'template' => "🔔 *RECORDATORIO DE PAGO*\n\n📄 *Factura:* {numero}\n👤 *Cliente:* {cliente}\n💰 *Monto Pendiente:* {moneda} {precio}\n📅 *Fecha de Vencimiento:* {fecha_vencimiento}\n\n{datos_bancarios}📞 Para cualquier consulta, contáctenos.\nGracias por su atención.\n\n*MarcSoftware Solutions*"
    ],
    
    'pago_vencido' => [
        'titulo' => 'Pago Vencido',
        'emoji' => '⚠️',
        'template' => "⚠️ *RECORDATORIO DE PAGO VENCIDO*\n\nSu factura tiene *{dias_vencido} días de vencida*\n\n📄 *Factura:* {numero}\n👤 *Cliente:* {cliente}\n💰 *Monto Pendiente:* {moneda} {precio}\n📅 *Fecha de Vencimiento:* {fecha_vencimiento}\n\n{datos_bancarios}Por favor, regularice su pago a la brevedad posible.\n\n*MarcSoftware Solutions*"
    ],
    
    'pago_confirmado' => [
        'titulo' => 'Pago Confirmado',
        'emoji' => '✅',
        'template' => "✅ *PAGO CONFIRMADO*\n\n¡Gracias por su pago!\n\n📄 *Factura:* {numero}\n💰 *Monto Pagado:* {moneda} {precio}\n📅 *Fecha de Pago:* {fecha_pago}\n\nSu factura ha sido marcada como *PAGADA* en nuestro sistema.\n\n✨ Gracias por elegir *MarcSoftware Solutions*\n🚀 Esperamos seguir trabajando juntos"
    ],
    
    'bienvenida' => [
        'titulo' => 'Mensaje de Bienvenida',
        'emoji' => '🎉',
        'template' => "🎉 *¡BIENVENIDO A MARCSOFTWARE SOLUTIONS!*\n\nHola *{cliente}*,\n\nGracias por confiar en nosotros para sus proyectos de desarrollo de software.\n\n🚀 *Nuestros Servicios:*\n• Desarrollo de aplicaciones web\n• Sistemas de gestión médica\n• Consultoría tecnológica\n• Soporte técnico especializado\n\n📞 *Contacto:*\n• WhatsApp: Este mismo número\n• Email: <EMAIL>\n\n¡Estamos aquí para ayudarle a hacer realidad sus ideas! 💡"
    ],
    
    'soporte_tecnico' => [
        'titulo' => 'Soporte Técnico',
        'emoji' => '🛠️',
        'template' => "🛠️ *SOPORTE TÉCNICO MARCSOFTWARE*\n\nHola,\n\nHemos recibido su solicitud de soporte técnico.\n\n📋 *Información del Ticket:*\n• Cliente: {cliente}\n• Fecha: {fecha}\n• Prioridad: {prioridad}\n\nNuestro equipo técnico se pondrá en contacto con usted en las próximas horas.\n\n⏰ *Horario de Atención:*\n{horario_atencion}\n\n*MarcSoftware Solutions*\n🚀 Su socio tecnológico de confianza"
    ]
];

// Configuración de automatización
$WHATSAPP_CONFIG = [
    'recordatorios_automaticos' => true,
    'dias_antes_vencimiento' => [7, 3, 1],  // Días antes del vencimiento para enviar recordatorios
    'dias_despues_vencimiento' => [1, 7, 15, 30],  // Días después del vencimiento
    'horario_envio_inicio' => '09:00',
    'horario_envio_fin' => '17:00',
    'enviar_fines_semana' => false,
    'max_recordatorios_por_dia' => 50,
    'intervalo_entre_recordatorios' => 300  // 5 minutos en segundos
];

// Configuración de integración con APIs externas
$WHATSAPP_API_CONFIG = [
    'usar_api_externa' => false,  // Cambiar a true si se usa Twilio u otra API
    'twilio' => [
        'account_sid' => '',
        'auth_token' => '',
        'whatsapp_number' => ''
    ],
    'whatsapp_business_api' => [
        'access_token' => '',
        'phone_number_id' => '',
        'webhook_verify_token' => ''
    ]
];

// Funciones helper para configuración
function obtener_numero_whatsapp($tipo = 'soporte') {
    switch ($tipo) {
        case 'ventas':
            return WHATSAPP_VENTAS;
        case 'facturacion':
            return WHATSAPP_FACTURACION;
        case 'soporte':
        default:
            return WHATSAPP_SOPORTE;
    }
}

function obtener_plantilla_mensaje($tipo) {
    global $WHATSAPP_PLANTILLAS;
    return $WHATSAPP_PLANTILLAS[$tipo] ?? null;
}

function esta_en_horario_atencion() {
    $hora_actual = date('H:i');
    $dia_semana = date('N'); // 1 = Lunes, 7 = Domingo
    
    $en_horario = ($hora_actual >= WHATSAPP_HORARIO_INICIO && $hora_actual <= WHATSAPP_HORARIO_FIN);
    $es_dia_laboral = ($dia_semana >= 1 && $dia_semana <= 5); // Lunes a Viernes
    
    return $en_horario && $es_dia_laboral;
}

function formatear_datos_bancarios($banco, $cuenta, $beneficiario) {
    if (empty($banco)) {
        return '';
    }
    
    return "🏦 *DATOS BANCARIOS:*\n" .
           "• *Banco:* {$banco}\n" .
           "• *Cuenta:* {$cuenta}\n" .
           "• *Beneficiario:* {$beneficiario}\n\n" .
           "📱 *Importante:* Envíe el comprobante de transferencia para confirmar el pago.\n\n";
}

function generar_url_whatsapp($telefono, $mensaje, $usar_web = false) {
    // Limpiar número de teléfono
    $telefono_limpio = preg_replace('/[^0-9]/', '', $telefono);
    
    // Agregar código de país si es necesario
    if (strlen($telefono_limpio) === 10) {
        $telefono_limpio = WHATSAPP_CODIGO_PAIS_DEFAULT . $telefono_limpio;
    } elseif (strlen($telefono_limpio) === 7) {
        $telefono_limpio = WHATSAPP_CODIGO_PAIS_DEFAULT . $telefono_limpio;
    }
    
    $mensaje_codificado = urlencode($mensaje);
    
    if ($usar_web) {
        return "https://web.whatsapp.com/send?phone={$telefono_limpio}&text={$mensaje_codificado}";
    } else {
        return "https://wa.me/{$telefono_limpio}?text={$mensaje_codificado}";
    }
}

function log_mensaje_whatsapp($factura_id, $telefono, $mensaje, $tipo, $usuario = null) {
    global $pdo;
    
    try {
        // Crear tabla de logs si no existe
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS WHATSAPP_LOGS (
                ID INT AUTO_INCREMENT PRIMARY KEY,
                FACTURA_ID INT,
                TELEFONO VARCHAR(20),
                MENSAJE TEXT,
                TIPO VARCHAR(50),
                USUARIO VARCHAR(100),
                FECHA_ENVIO DATETIME DEFAULT CURRENT_TIMESTAMP,
                ESTADO VARCHAR(20) DEFAULT 'ENVIADO',
                IP_ADDRESS VARCHAR(45),
                USER_AGENT TEXT,
                INDEX idx_factura (FACTURA_ID),
                INDEX idx_fecha (FECHA_ENVIO),
                INDEX idx_tipo (TIPO)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $stmt = $pdo->prepare("
            INSERT INTO WHATSAPP_LOGS 
            (FACTURA_ID, TELEFONO, MENSAJE, TIPO, USUARIO, IP_ADDRESS, USER_AGENT)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $factura_id,
            $telefono,
            $mensaje,
            $tipo,
            $usuario ?: ($_SESSION['usuario'] ?? 'Sistema'),
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("Error logging WhatsApp message: " . $e->getMessage());
        return false;
    }
}

// Configuración de estilos CSS para WhatsApp
function obtener_css_whatsapp() {
    return '
    <style>
        .whatsapp-btn {
            background-color: #25d366 !important;
            border-color: #25d366 !important;
            color: white !important;
        }
        
        .whatsapp-btn:hover {
            background-color: #128c7e !important;
            border-color: #128c7e !important;
            color: white !important;
        }
        
        .whatsapp-float {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .whatsapp-widget {
            border-left: 4px solid #25d366;
            background: linear-gradient(135deg, #f0fff4, #e8f5e8);
        }
        
        .whatsapp-icon {
            color: #25d366;
            font-size: 1.2em;
        }
    </style>';
}
?>
