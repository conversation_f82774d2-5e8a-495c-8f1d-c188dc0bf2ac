<?php
/**
 * Sistema de Recordatorios Automáticos de Citas - MarcSoftware Solutions
 * Configuración y ejecución de recordatorios automáticos por WhatsApp
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], ['admin', 'doctor', 'secretaria'])) {
    header('Location: ../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/whatsapp_manager.php';
require_once __DIR__ . '/config_whatsapp.php';

$whatsapp = new WhatsAppManager();

// Variables para mensajes
$success_message = '';
$error_message = '';
$warning_message = '';

// Crear tabla de configuración si no existe
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS CONFIG_RECORDATORIOS_CITAS (
            ID INT AUTO_INCREMENT PRIMARY KEY,
            ACTIVO BOOLEAN DEFAULT TRUE,
            DIAS_ANTICIPACION JSON,
            HORARIO_INICIO TIME DEFAULT '09:00:00',
            HORARIO_FIN TIME DEFAULT '17:00:00',
            ENVIAR_FINES_SEMANA BOOLEAN DEFAULT FALSE,
            MAX_RECORDATORIOS_DIA INT DEFAULT 50,
            PLANTILLA_MENSAJE TEXT,
            FECHA_ACTUALIZACION DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            USUARIO_ACTUALIZACION VARCHAR(100)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    
    // Insertar configuración por defecto si no existe
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM CONFIG_RECORDATORIOS_CITAS");
    if ($stmt->fetch()['count'] == 0) {
        $pdo->exec("
            INSERT INTO CONFIG_RECORDATORIOS_CITAS 
            (DIAS_ANTICIPACION, PLANTILLA_MENSAJE, USUARIO_ACTUALIZACION) 
            VALUES 
            ('[3, 1]', 'Plantilla por defecto', 'Sistema')
        ");
    }
} catch (Exception $e) {
    $error_message = "Error al crear configuración: " . $e->getMessage();
}

// Obtener configuración actual
try {
    $stmt = $pdo->query("SELECT * FROM CONFIG_RECORDATORIOS_CITAS ORDER BY ID DESC LIMIT 1");
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config) {
        $config['DIAS_ANTICIPACION'] = json_decode($config['DIAS_ANTICIPACION'], true);
    }
} catch (Exception $e) {
    $config = null;
    $warning_message = "No se pudo cargar la configuración actual";
}

// Procesar actualización de configuración
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['actualizar_config'])) {
    $activo = isset($_POST['activo']) ? 1 : 0;
    $dias_anticipacion = array_map('intval', explode(',', $_POST['dias_anticipacion']));
    $horario_inicio = $_POST['horario_inicio'];
    $horario_fin = $_POST['horario_fin'];
    $enviar_fines_semana = isset($_POST['enviar_fines_semana']) ? 1 : 0;
    $max_recordatorios = intval($_POST['max_recordatorios']);
    $plantilla_mensaje = $_POST['plantilla_mensaje'];
    
    try {
        $stmt = $pdo->prepare("
            UPDATE CONFIG_RECORDATORIOS_CITAS 
            SET ACTIVO = ?, 
                DIAS_ANTICIPACION = ?, 
                HORARIO_INICIO = ?, 
                HORARIO_FIN = ?, 
                ENVIAR_FINES_SEMANA = ?, 
                MAX_RECORDATORIOS_DIA = ?, 
                PLANTILLA_MENSAJE = ?,
                USUARIO_ACTUALIZACION = ?
            ORDER BY ID DESC LIMIT 1
        ");
        
        $stmt->execute([
            $activo,
            json_encode($dias_anticipacion),
            $horario_inicio,
            $horario_fin,
            $enviar_fines_semana,
            $max_recordatorios,
            $plantilla_mensaje,
            $_SESSION['usuario']
        ]);
        
        $success_message = "Configuración actualizada correctamente";
        
        // Recargar configuración
        $stmt = $pdo->query("SELECT * FROM CONFIG_RECORDATORIOS_CITAS ORDER BY ID DESC LIMIT 1");
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        $config['DIAS_ANTICIPACION'] = json_decode($config['DIAS_ANTICIPACION'], true);
        
    } catch (Exception $e) {
        $error_message = "Error al actualizar configuración: " . $e->getMessage();
    }
}

// Ejecutar recordatorios automáticos
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ejecutar_recordatorios'])) {
    $resultados = ejecutarRecordatoriosAutomaticos($pdo, $whatsapp, $config);
    
    if ($resultados['success']) {
        $success_message = "Recordatorios ejecutados: {$resultados['enviados']} mensajes preparados";
        if ($resultados['errores'] > 0) {
            $warning_message = "Se encontraron {$resultados['errores']} errores durante la ejecución";
        }
    } else {
        $error_message = "Error al ejecutar recordatorios: " . $resultados['error'];
    }
}

// Función para ejecutar recordatorios automáticos
function ejecutarRecordatoriosAutomaticos($pdo, $whatsapp, $config) {
    if (!$config || !$config['ACTIVO']) {
        return ['success' => false, 'error' => 'Recordatorios automáticos desactivados'];
    }
    
    $enviados = 0;
    $errores = 0;
    $mensajes_generados = [];
    
    try {
        foreach ($config['DIAS_ANTICIPACION'] as $dias) {
            $fecha_objetivo = date('Y-m-d', strtotime("+{$dias} days"));
            
            // Obtener citas para la fecha objetivo
            $stmt = $pdo->prepare("
                SELECT 
                    c.*,
                    p.NOMBREAPELLIDO as paciente_nombre,
                    p.TELEFONO as paciente_telefono,
                    p.CELULAR as paciente_celular,
                    p.CEDULA as paciente_cedula
                FROM CITAMEDIC c
                LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE OR c.NSS = p.CEDULA
                WHERE c.FECHACON = ?
                AND c.ESTATUS IN (1, 3, 6)
                AND (p.TELEFONO IS NOT NULL OR p.CELULAR IS NOT NULL)
            ");
            
            $stmt->execute([$fecha_objetivo]);
            $citas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($citas as $cita) {
                // Verificar si ya se envió recordatorio para esta cita y días de anticipación
                $stmt_check = $pdo->prepare("
                    SELECT COUNT(*) as count 
                    FROM RECORDATORIOS_CITAS_WHATSAPP 
                    WHERE CITA_ID = ? AND DIAS_ANTICIPACION = ? AND DATE(FECHA_ENVIO) = CURDATE()
                ");
                $stmt_check->execute([$cita['CLAVE'], $dias]);
                
                if ($stmt_check->fetch()['count'] > 0) {
                    continue; // Ya se envió hoy
                }
                
                $telefono = !empty($cita['paciente_celular']) ? $cita['paciente_celular'] : $cita['paciente_telefono'];
                
                if (!empty($telefono)) {
                    // Generar mensaje
                    $cita['dias_hasta_cita'] = $dias;
                    $mensaje = generarMensajeRecordatorioCita($cita);
                    
                    // Registrar en base de datos
                    $stmt_log = $pdo->prepare("
                        INSERT INTO RECORDATORIOS_CITAS_WHATSAPP 
                        (CITA_ID, PACIENTE_TELEFONO, MENSAJE, USUARIO_ENVIO, TIPO_RECORDATORIO, DIAS_ANTICIPACION)
                        VALUES (?, ?, ?, ?, 'AUTOMATICO', ?)
                    ");
                    
                    $stmt_log->execute([
                        $cita['CLAVE'],
                        $telefono,
                        $mensaje,
                        'Sistema',
                        $dias
                    ]);
                    
                    $mensajes_generados[] = [
                        'paciente' => $cita['paciente_nombre'],
                        'telefono' => $telefono,
                        'mensaje' => $mensaje,
                        'url' => $whatsapp->generateWhatsAppURL($telefono, $mensaje)
                    ];
                    
                    $enviados++;
                }
            }
        }
        
        return [
            'success' => true,
            'enviados' => $enviados,
            'errores' => $errores,
            'mensajes' => $mensajes_generados
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Función para generar mensaje (reutilizada del archivo anterior)
function generarMensajeRecordatorioCita($cita) {
    $dias = $cita['dias_hasta_cita'];
    $fecha_cita = date('d/m/Y', strtotime($cita['FECHACON']));
    $hora_cita = date('h:i A', strtotime($cita['HORACON']));
    
    if ($dias == 0) {
        $cuando = "HOY";
        $emoji = "🚨";
    } elseif ($dias == 1) {
        $cuando = "MAÑANA";
        $emoji = "⏰";
    } else {
        $cuando = "en {$dias} días";
        $emoji = "📅";
    }
    
    $mensaje = "{$emoji} *RECORDATORIO DE CITA MÉDICA*\n\n";
    $mensaje .= "Estimado/a *{$cita['paciente_nombre']}*,\n\n";
    $mensaje .= "Le recordamos que tiene una cita médica programada para *{$cuando}*:\n\n";
    $mensaje .= "📅 *Fecha:* {$fecha_cita}\n";
    $mensaje .= "🕐 *Hora:* {$hora_cita}\n";
    
    if (!empty($cita['CONSULTORIO'])) {
        $mensaje .= "🏥 *Consultorio:* {$cita['CONSULTORIO']}\n";
    }
    
    if (!empty($cita['OBSERVACION'])) {
        $mensaje .= "📝 *Observaciones:* {$cita['OBSERVACION']}\n";
    }
    
    $mensaje .= "\n📋 *Recomendaciones:*\n";
    $mensaje .= "• Llegue 15 minutos antes de su cita\n";
    $mensaje .= "• Traiga su cédula de identidad\n";
    $mensaje .= "• Traiga su tarjeta de seguro (si aplica)\n";
    $mensaje .= "• Si tiene estudios previos, tráigalos\n\n";
    
    if ($dias <= 1) {
        $mensaje .= "⚠️ *IMPORTANTE:* Si no puede asistir, por favor comuníquese con nosotros lo antes posible.\n\n";
    } else {
        $mensaje .= "Si necesita reprogramar su cita, contáctenos con anticipación.\n\n";
    }
    
    $mensaje .= "📞 *Contacto:* " . WHATSAPP_SOPORTE . "\n";
    $mensaje .= "🏥 *Consultorio Médico*\n";
    $mensaje .= "Cuidando su salud con profesionalismo ❤️";
    
    return $mensaje;
}

// Obtener estadísticas de recordatorios
try {
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_recordatorios,
            COUNT(CASE WHEN DATE(FECHA_ENVIO) = CURDATE() THEN 1 END) as hoy,
            COUNT(CASE WHEN FECHA_ENVIO >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as esta_semana,
            COUNT(CASE WHEN TIPO_RECORDATORIO = 'AUTOMATICO' THEN 1 END) as automaticos
        FROM RECORDATORIOS_CITAS_WHATSAPP
    ");
    $stats_recordatorios = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $stats_recordatorios = ['total_recordatorios' => 0, 'hoy' => 0, 'esta_semana' => 0, 'automaticos' => 0];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recordatorios Automáticos - Consultorio Médico</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <style>
        .config-section { background: linear-gradient(135deg, #f8f9fa, #e9ecef); }
        .stats-card { transition: transform 0.2s; }
        .stats-card:hover { transform: translateY(-2px); }
        .whatsapp-btn { background-color: #25d366; border-color: #25d366; }
        .whatsapp-btn:hover { background-color: #128c7e; border-color: #128c7e; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-robot text-primary"></i>
                    Recordatorios Automáticos de Citas
                </h1>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="bi bi-check-circle"></i> <?= $success_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="bi bi-exclamation-triangle"></i> <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($warning_message): ?>
                    <div class="alert alert-warning alert-dismissible fade show">
                        <i class="bi bi-exclamation-triangle"></i> <?= $warning_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Estadísticas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center stats-card border-primary">
                            <div class="card-body">
                                <h5 class="card-title text-primary"><?= $stats_recordatorios['total_recordatorios'] ?></h5>
                                <p class="card-text">Total Recordatorios</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center stats-card border-success">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?= $stats_recordatorios['hoy'] ?></h5>
                                <p class="card-text">Enviados Hoy</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center stats-card border-info">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?= $stats_recordatorios['esta_semana'] ?></h5>
                                <p class="card-text">Esta Semana</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center stats-card border-warning">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?= $stats_recordatorios['automaticos'] ?></h5>
                                <p class="card-text">Automáticos</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Configuración -->
                <div class="card mb-4 config-section">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> Configuración de Recordatorios Automáticos</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="activo" name="activo" 
                                                   <?= ($config && $config['ACTIVO']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="activo">
                                                <strong>Activar Recordatorios Automáticos</strong>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="dias_anticipacion" class="form-label">Días de Anticipación</label>
                                        <input type="text" class="form-control" id="dias_anticipacion" name="dias_anticipacion" 
                                               value="<?= $config ? implode(',', $config['DIAS_ANTICIPACION']) : '3,1' ?>"
                                               placeholder="3,1">
                                        <div class="form-text">Separar con comas. Ej: 3,1 (3 días antes y 1 día antes)</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max_recordatorios" class="form-label">Máximo Recordatorios por Día</label>
                                        <input type="number" class="form-control" id="max_recordatorios" name="max_recordatorios" 
                                               value="<?= $config['MAX_RECORDATORIOS_DIA'] ?? 50 ?>" min="1" max="200">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="horario_inicio" class="form-label">Horario de Inicio</label>
                                        <input type="time" class="form-control" id="horario_inicio" name="horario_inicio" 
                                               value="<?= $config['HORARIO_INICIO'] ?? '09:00' ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="horario_fin" class="form-label">Horario de Fin</label>
                                        <input type="time" class="form-control" id="horario_fin" name="horario_fin" 
                                               value="<?= $config['HORARIO_FIN'] ?? '17:00' ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enviar_fines_semana" name="enviar_fines_semana" 
                                                   <?= ($config && $config['ENVIAR_FINES_SEMANA']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="enviar_fines_semana">
                                                Enviar en fines de semana
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="plantilla_mensaje" class="form-label">Plantilla de Mensaje (Opcional)</label>
                                <textarea class="form-control" id="plantilla_mensaje" name="plantilla_mensaje" rows="3" 
                                          placeholder="Personalizar mensaje base..."><?= htmlspecialchars($config['PLANTILLA_MENSAJE'] ?? '') ?></textarea>
                                <div class="form-text">Si se deja vacío, se usará la plantilla por defecto</div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" name="actualizar_config" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Guardar Configuración
                                </button>
                                <button type="submit" name="ejecutar_recordatorios" class="btn btn-success">
                                    <i class="bi bi-play-circle"></i> Ejecutar Ahora
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Información sobre automatización -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-info-circle"></i> Automatización con Cron Jobs</h5>
                    </div>
                    <div class="card-body">
                        <p>Para que los recordatorios se ejecuten automáticamente, configure un cron job en su servidor:</p>
                        <div class="alert alert-info">
                            <strong>Comando sugerido:</strong><br>
                            <code>0 9 * * * /usr/bin/php <?= __DIR__ ?>/ejecutar_recordatorios_cron.php</code>
                        </div>
                        <p><small class="text-muted">Este comando ejecutará los recordatorios todos los días a las 9:00 AM</small></p>
                        
                        <a href="crear_cron_job.php" class="btn btn-outline-primary">
                            <i class="bi bi-download"></i> Descargar Script Cron
                        </a>
                    </div>
                </div>
                
                <!-- Botones de navegación -->
                <div class="mt-4">
                    <a href="recordatorios_citas.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Volver a Recordatorios
                    </a>
                    <a href="../../index.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Inicio
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
