<?php
/**
 * Sistema de Recordatorios WhatsApp - MarcSoftware Solutions
 * Gestión automática de recordatorios de pago por WhatsApp
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], ['admin', 'secretaria'])) {
    header('Location: ../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/whatsapp_manager.php';

$whatsapp = new WhatsAppManager();

// Obtener facturas pendientes
try {
    $stmt = $pdo->query("
        SELECT 
            fs.*,
            cs.NOMBRE_COMPLETO as cliente_nombre,
            cs.TELEFONO as cliente_telefono,
            cs.EMAIL as cliente_email,
            DATEDIFF(CURDATE(), fs.FECHA_VENCIMIENTO) as dias_vencido
        FROM FACTURAS_SOFTWARE fs
        JOIN CLIENTES_SOFTWARE cs ON fs.CLIENTE_CLAVE = cs.CLAVE
        WHERE fs.ESTATUS = 'PENDIENTE'
        ORDER BY fs.FECHA_VENCIMIENTO ASC
    ");
    $facturas_pendientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $facturas_pendientes = [];
    $error_message = "Error al cargar facturas: " . $e->getMessage();
}

// Procesar envío de recordatorio
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enviar_recordatorio'])) {
    $factura_id = $_POST['factura_id'];
    
    // Buscar la factura específica
    $stmt = $pdo->prepare("
        SELECT 
            fs.*,
            cs.NOMBRE_COMPLETO as cliente_nombre,
            cs.TELEFONO as cliente_telefono,
            cs.EMAIL as cliente_email,
            DATEDIFF(CURDATE(), fs.FECHA_VENCIMIENTO) as dias_vencido
        FROM FACTURAS_SOFTWARE fs
        JOIN CLIENTES_SOFTWARE cs ON fs.CLIENTE_CLAVE = cs.CLAVE
        WHERE fs.CLAVE = ?
    ");
    $stmt->execute([$factura_id]);
    $factura = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($factura) {
        $factura_data = [
            'numero' => $factura['NUMERO_FACTURA'],
            'cliente' => $factura['cliente_nombre'],
            'precio' => $factura['PRECIO'],
            'moneda' => $factura['MONEDA'],
            'fecha_vencimiento' => $factura['FECHA_VENCIMIENTO'],
            'modo_pago' => $factura['MODO_PAGO'],
            'banco' => $factura['BANCO_DESTINO'],
            'cuenta' => $factura['CUENTA_DESTINO'],
            'beneficiario' => $factura['BENEFICIARIO']
        ];
        
        $mensaje = $whatsapp->generatePaymentReminderMessage($factura_data, max(0, $factura['dias_vencido']));
        $telefono = $factura['cliente_telefono'];
        
        // Registrar el recordatorio en la base de datos
        try {
            $stmt_log = $pdo->prepare("
                INSERT INTO RECORDATORIOS_WHATSAPP 
                (FACTURA_ID, CLIENTE_TELEFONO, MENSAJE, FECHA_ENVIO, USUARIO_ENVIO, TIPO_RECORDATORIO)
                VALUES (?, ?, ?, NOW(), ?, ?)
            ");
            
            // Crear tabla si no existe
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS RECORDATORIOS_WHATSAPP (
                    ID INT AUTO_INCREMENT PRIMARY KEY,
                    FACTURA_ID INT NOT NULL,
                    CLIENTE_TELEFONO VARCHAR(20),
                    MENSAJE TEXT,
                    FECHA_ENVIO DATETIME,
                    USUARIO_ENVIO VARCHAR(100),
                    TIPO_RECORDATORIO VARCHAR(20) DEFAULT 'MANUAL',
                    ESTADO VARCHAR(20) DEFAULT 'ENVIADO',
                    INDEX idx_factura (FACTURA_ID),
                    INDEX idx_fecha (FECHA_ENVIO)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            $tipo = $factura['dias_vencido'] > 0 ? 'VENCIDO' : 'RECORDATORIO';
            $stmt_log->execute([
                $factura_id,
                $telefono,
                $mensaje,
                $_SESSION['usuario'],
                $tipo
            ]);
            
            $success_message = "Recordatorio preparado para envío";
            $whatsapp_url = $whatsapp->generateWhatsAppURL($telefono, $mensaje);
            $show_whatsapp = true;
            
        } catch (Exception $e) {
            $error_message = "Error al registrar recordatorio: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recordatorios WhatsApp - MarcSoftware Solutions</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <style>
        .overdue { background-color: #fff5f5; border-left: 4px solid #dc3545; }
        .due-soon { background-color: #fff8e1; border-left: 4px solid #ffc107; }
        .whatsapp-btn { background-color: #25d366; border-color: #25d366; }
        .whatsapp-btn:hover { background-color: #128c7e; border-color: #128c7e; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-whatsapp text-success"></i>
                    Recordatorios de Pago - WhatsApp
                </h1>
                
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="bi bi-check-circle"></i> <?= $success_message ?>
                        <?php if (isset($show_whatsapp) && $show_whatsapp): ?>
                            <hr>
                            <div class="d-grid gap-2 d-md-flex">
                                <a href="<?= htmlspecialchars($whatsapp_url) ?>" target="_blank" class="btn whatsapp-btn text-white">
                                    <i class="bi bi-whatsapp"></i> Abrir WhatsApp
                                </a>
                                <button type="button" class="btn btn-outline-success" onclick="copyMessage()">
                                    <i class="bi bi-clipboard"></i> Copiar Mensaje
                                </button>
                            </div>
                            <script>
                                const mensaje = `<?= addslashes($mensaje ?? '') ?>`;
                                function copyMessage() {
                                    navigator.clipboard.writeText(mensaje).then(() => {
                                        alert('✅ Mensaje copiado al portapapeles');
                                    });
                                }
                            </script>
                        <?php endif; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="bi bi-exclamation-triangle"></i> <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Estadísticas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger"><?= count(array_filter($facturas_pendientes, fn($f) => $f['dias_vencido'] > 0)) ?></h5>
                                <p class="card-text">Facturas Vencidas</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?= count(array_filter($facturas_pendientes, fn($f) => $f['dias_vencido'] <= 0 && $f['dias_vencido'] >= -7)) ?></h5>
                                <p class="card-text">Vencen Pronto</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?= count($facturas_pendientes) ?></h5>
                                <p class="card-text">Total Pendientes</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?= count(array_filter($facturas_pendientes, fn($f) => !empty($f['cliente_telefono']))) ?></h5>
                                <p class="card-text">Con WhatsApp</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Lista de Facturas Pendientes -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-list-check"></i> Facturas Pendientes de Pago</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($facturas_pendientes)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                No hay facturas pendientes de pago.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Factura</th>
                                            <th>Cliente</th>
                                            <th>Monto</th>
                                            <th>Vencimiento</th>
                                            <th>Estado</th>
                                            <th>Teléfono</th>
                                            <th>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($facturas_pendientes as $factura): ?>
                                            <tr class="<?= $factura['dias_vencido'] > 0 ? 'overdue' : ($factura['dias_vencido'] >= -7 ? 'due-soon' : '') ?>">
                                                <td>
                                                    <strong><?= htmlspecialchars($factura['NUMERO_FACTURA']) ?></strong>
                                                </td>
                                                <td><?= htmlspecialchars($factura['cliente_nombre']) ?></td>
                                                <td>
                                                    <strong><?= $factura['MONEDA'] ?> <?= number_format($factura['PRECIO'], 2) ?></strong>
                                                </td>
                                                <td>
                                                    <?= date('d/m/Y', strtotime($factura['FECHA_VENCIMIENTO'])) ?>
                                                    <?php if ($factura['dias_vencido'] > 0): ?>
                                                        <br><small class="text-danger">
                                                            <i class="bi bi-exclamation-triangle"></i>
                                                            <?= $factura['dias_vencido'] ?> días vencida
                                                        </small>
                                                    <?php elseif ($factura['dias_vencido'] >= -7): ?>
                                                        <br><small class="text-warning">
                                                            <i class="bi bi-clock"></i>
                                                            Vence en <?= abs($factura['dias_vencido']) ?> días
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning">PENDIENTE</span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($factura['cliente_telefono'])): ?>
                                                        <i class="bi bi-whatsapp text-success"></i>
                                                        <?= htmlspecialchars($factura['cliente_telefono']) ?>
                                                    <?php else: ?>
                                                        <small class="text-muted">No disponible</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($factura['cliente_telefono'])): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="factura_id" value="<?= $factura['CLAVE'] ?>">
                                                            <button type="submit" name="enviar_recordatorio" class="btn btn-sm whatsapp-btn text-white">
                                                                <i class="bi bi-whatsapp"></i> Recordatorio
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary" disabled>
                                                            <i class="bi bi-telephone-x"></i> Sin teléfono
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Botones de acción -->
                <div class="mt-4">
                    <a href="../facturas/lista_facturas.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Volver a Facturas
                    </a>
                    <a href="../../index.php" class="btn btn-primary">
                        <i class="bi bi-house"></i> Inicio
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
