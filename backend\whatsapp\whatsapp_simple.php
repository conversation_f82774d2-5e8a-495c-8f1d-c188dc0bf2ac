<?php
/**
 * Sistema Simple de WhatsApp para Consultorio Médico
 * Usando enlaces de WhatsApp Web (NO requiere API)
 * 
 * <AUTHOR> de Consultorio
 * @version 1.0
 * @date 2024
 */

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], ['admin', 'doctor', 'secretaria'])) {
    header('Location: ../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';

class WhatsAppSimple {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Formatear número de teléfono para WhatsApp
     */
    private function formatPhoneNumber($phone) {
        // Remover caracteres no numéricos
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Si el número empieza con 8 o 9 (República Dominicana), agregar código de país
        if (strlen($phone) == 10 && (substr($phone, 0, 1) == '8' || substr($phone, 0, 1) == '9')) {
            $phone = '1' . $phone; // +1 para República Dominicana
        }
        
        return $phone;
    }
    
    /**
     * Generar enlace de WhatsApp
     */
    public function generateWhatsAppLink($phone, $message) {
        $phone = $this->formatPhoneNumber($phone);
        $encodedMessage = urlencode($message);
        return "https://wa.me/{$phone}?text={$encodedMessage}";
    }
    
    /**
     * Obtener citas del día
     */
    public function getCitasHoy() {
        $hoy = date('Y-m-d');
        $stmt = $this->pdo->prepare("
            SELECT c.*, p.NOMBRES, p.APELLIDOS, p.TELEFONO, p.CELULAR, p.ECORREO
            FROM CITAMEDIC c
            LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
            WHERE c.FECHACON = ? AND c.ESTATUS IN (3, 6)
            ORDER BY c.HORACON
        ");
        $stmt->execute([$hoy]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Obtener citas de mañana
     */
    public function getCitasManana() {
        $manana = date('Y-m-d', strtotime('+1 day'));
        $stmt = $this->pdo->prepare("
            SELECT c.*, p.NOMBRES, p.APELLIDOS, p.TELEFONO, p.CELULAR, p.ECORREO
            FROM CITAMEDIC c
            LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
            WHERE c.FECHACON = ? AND c.ESTATUS IN (3, 6)
            ORDER BY c.HORACON
        ");
        $stmt->execute([$manana]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Generar mensaje de confirmación de cita
     */
    public function generarMensajeConfirmacion($cita) {
        $fecha = date('d/m/Y', strtotime($cita['FECHACON']));
        $hora = date('g:i A', strtotime($cita['HORACON']));
        $nombre = trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS']);
        
        $mensaje = "🏥 *CONFIRMACIÓN DE CITA MÉDICA*\n\n";
        $mensaje .= "Estimado/a *{$nombre}*,\n\n";
        $mensaje .= "Su cita médica ha sido confirmada:\n";
        $mensaje .= "📅 *Fecha:* {$fecha}\n";
        $mensaje .= "🕐 *Hora:* {$hora}\n";
        $mensaje .= "🏥 *Consultorio:* " . ($cita['CONSULTORIO'] ?? 'Principal') . "\n\n";
        $mensaje .= "📋 *Recomendaciones:*\n";
        $mensaje .= "• Llegar 15 minutos antes\n";
        $mensaje .= "• Traer documento de identidad\n";
        $mensaje .= "• Traer carnet del seguro (si aplica)\n\n";
        $mensaje .= "Para reprogramar o cancelar, contacte al consultorio.\n\n";
        $mensaje .= "¡Esperamos verle pronto! 👩‍⚕️👨‍⚕️";
        
        return $mensaje;
    }
    
    /**
     * Generar mensaje de recordatorio
     */
    public function generarMensajeRecordatorio($cita) {
        $fecha = date('d/m/Y', strtotime($cita['FECHACON']));
        $hora = date('g:i A', strtotime($cita['HORACON']));
        $nombre = trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS']);
        
        $mensaje = "⏰ *RECORDATORIO DE CITA MÉDICA*\n\n";
        $mensaje .= "Estimado/a *{$nombre}*,\n\n";
        $mensaje .= "Le recordamos su cita médica para mañana:\n";
        $mensaje .= "📅 *Fecha:* {$fecha}\n";
        $mensaje .= "🕐 *Hora:* {$hora}\n";
        $mensaje .= "🏥 *Consultorio:* " . ($cita['CONSULTORIO'] ?? 'Principal') . "\n\n";
        $mensaje .= "📝 *No olvide traer:*\n";
        $mensaje .= "• Documento de identidad\n";
        $mensaje .= "• Carnet del seguro\n";
        $mensaje .= "• Estudios médicos previos\n\n";
        $mensaje .= "Si no puede asistir, por favor contacte al consultorio.\n\n";
        $mensaje .= "¡Nos vemos mañana! 🩺";
        
        return $mensaje;
    }
    
    /**
     * Registrar acción en base de datos
     */
    public function registrarAccion($telefono, $mensaje, $tipo, $paciente_id = null) {
        // Crear tabla de logs si no existe
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS WHATSAPP_LOGS_SIMPLE (
                ID INT AUTO_INCREMENT PRIMARY KEY,
                PACIENTE_ID INT,
                TELEFONO VARCHAR(20),
                MENSAJE TEXT,
                TIPO VARCHAR(50),
                FECHA_GENERACION DATETIME DEFAULT CURRENT_TIMESTAMP,
                ESTADO VARCHAR(20) DEFAULT 'GENERADO',
                INDEX idx_paciente (PACIENTE_ID),
                INDEX idx_fecha (FECHA_GENERACION),
                INDEX idx_tipo (TIPO)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $stmt = $this->pdo->prepare("
            INSERT INTO WHATSAPP_LOGS_SIMPLE (PACIENTE_ID, TELEFONO, MENSAJE, TIPO)
            VALUES (?, ?, ?, ?)
        ");
        
        return $stmt->execute([$paciente_id, $telefono, $mensaje, $tipo]);
    }
}

// Inicializar el sistema
$whatsapp = new WhatsAppSimple($pdo);

// Obtener datos
$citas_hoy = $whatsapp->getCitasHoy();
$citas_manana = $whatsapp->getCitasManana();

// Procesar acciones
$mensaje_resultado = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    
    if ($accion === 'generar_enlaces') {
        $tipo = $_POST['tipo'] ?? '';
        $citas = ($tipo === 'hoy') ? $citas_hoy : $citas_manana;
        
        foreach ($citas as $cita) {
            $telefono = $cita['CELULAR'] ?: $cita['TELEFONO'];
            if ($telefono) {
                $mensaje = ($tipo === 'hoy') ? 
                    $whatsapp->generarMensajeConfirmacion($cita) : 
                    $whatsapp->generarMensajeRecordatorio($cita);
                
                $whatsapp->registrarAccion(
                    $telefono,
                    $mensaje,
                    ($tipo === 'hoy') ? 'CONFIRMACION_GENERADA' : 'RECORDATORIO_GENERADO',
                    $cita['CLAVEPAC']
                );
            }
        }
        
        $mensaje_resultado = "✅ Enlaces generados correctamente. Revise la lista abajo.";
    }
}

// Obtener logs recientes
$stmt = $pdo->query("
    SELECT * FROM WHATSAPP_LOGS_SIMPLE 
    ORDER BY FECHA_GENERACION DESC 
    LIMIT 10
");
$logs_recientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Simple - Consultorio Médico</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <style>
        :root {
            --whatsapp-green: #25D366;
            --whatsapp-dark: #128C7E;
        }

        .whatsapp-header {
            background: linear-gradient(135deg, var(--whatsapp-green), var(--whatsapp-dark));
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .whatsapp-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }

        .btn-whatsapp {
            background: var(--whatsapp-green);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-whatsapp:hover {
            background: var(--whatsapp-dark);
            color: white;
            transform: translateY(-1px);
        }

        .btn-whatsapp-sm {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
        }

        .message-preview {
            background: #e7f3ff;
            border-left: 4px solid #0d6efd;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.85rem;
            white-space: pre-line;
            max-height: 200px;
            overflow-y: auto;
        }

        .info-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="whatsapp-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="bi bi-whatsapp"></i>
                        WhatsApp Simple
                    </h1>
                    <p class="mb-0 opacity-75">Sistema sin API - Usando enlaces de WhatsApp Web</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="../../index.php" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i>
                        Volver al Panel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Mensaje de resultado -->
        <?php if ($mensaje_resultado): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill"></i>
                <?= htmlspecialchars($mensaje_resultado) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Información importante -->
        <div class="info-box">
            <h5><i class="bi bi-info-circle-fill"></i> ¿Cómo funciona este sistema?</h5>
            <p class="mb-2">
                <strong>✅ Ventajas:</strong> No requiere API, es gratuito, fácil de usar
            </p>
            <p class="mb-2">
                <strong>📱 Funcionamiento:</strong> Genera enlaces que abren WhatsApp Web con el mensaje pre-escrito
            </p>
            <p class="mb-0">
                <strong>🔄 Proceso:</strong> 1) Generar enlaces → 2) Hacer clic en cada enlace → 3) Enviar mensaje desde WhatsApp Web
            </p>
        </div>

        <!-- Estadísticas -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card whatsapp-card text-center">
                    <div class="card-body">
                        <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                        <h3 class="mt-2"><?= count($citas_hoy) ?></h3>
                        <p class="text-muted mb-0">Citas Hoy</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card whatsapp-card text-center">
                    <div class="card-body">
                        <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                        <h3 class="mt-2"><?= count($citas_manana) ?></h3>
                        <p class="text-muted mb-0">Citas Mañana</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card whatsapp-card text-center">
                    <div class="card-body">
                        <i class="bi bi-chat-dots text-info" style="font-size: 2rem;"></i>
                        <h3 class="mt-2"><?= count($logs_recientes) ?></h3>
                        <p class="text-muted mb-0">Mensajes Generados</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones Rápidas -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card whatsapp-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-day"></i>
                            Citas de Hoy (<?= count($citas_hoy) ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($citas_hoy)): ?>
                            <p class="text-muted text-center">No hay citas para hoy</p>
                        <?php else: ?>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <?php foreach ($citas_hoy as $cita): ?>
                                    <?php
                                    $telefono = $cita['CELULAR'] ?: $cita['TELEFONO'];
                                    $nombre = trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS']);
                                    $hora = date('g:i A', strtotime($cita['HORACON']));
                                    ?>
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong><?= htmlspecialchars($nombre) ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-clock"></i> <?= $hora ?>
                                                    <i class="bi bi-telephone ms-2"></i> <?= htmlspecialchars($telefono ?: 'Sin teléfono') ?>
                                                </small>
                                            </div>
                                            <?php if ($telefono): ?>
                                                <a href="<?= $whatsapp->generateWhatsAppLink($telefono, $whatsapp->generarMensajeConfirmacion($cita)) ?>"
                                                   target="_blank" class="btn btn-whatsapp btn-whatsapp-sm">
                                                    <i class="bi bi-whatsapp"></i>
                                                    Enviar
                                                </a>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Sin teléfono</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card whatsapp-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-plus"></i>
                            Citas de Mañana (<?= count($citas_manana) ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($citas_manana)): ?>
                            <p class="text-muted text-center">No hay citas para mañana</p>
                        <?php else: ?>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <?php foreach ($citas_manana as $cita): ?>
                                    <?php
                                    $telefono = $cita['CELULAR'] ?: $cita['TELEFONO'];
                                    $nombre = trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS']);
                                    $hora = date('g:i A', strtotime($cita['HORACON']));
                                    ?>
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="flex-grow-1">
                                                <strong><?= htmlspecialchars($nombre) ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-clock"></i> <?= $hora ?>
                                                    <i class="bi bi-telephone ms-2"></i> <?= htmlspecialchars($telefono ?: 'Sin teléfono') ?>
                                                </small>
                                            </div>
                                            <?php if ($telefono): ?>
                                                <a href="<?= $whatsapp->generateWhatsAppLink($telefono, $whatsapp->generarMensajeRecordatorio($cita)) ?>"
                                                   target="_blank" class="btn btn-whatsapp btn-whatsapp-sm">
                                                    <i class="bi bi-whatsapp"></i>
                                                    Enviar
                                                </a>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Sin teléfono</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mensaje Personalizado -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card whatsapp-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-chat-text"></i>
                            Mensaje Personalizado
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="telefono" class="form-label">Número de Teléfono</label>
                                    <input type="tel" class="form-control" id="telefono"
                                           placeholder="Ej: 8091234567" required>
                                </div>

                                <div class="mb-3">
                                    <label for="mensaje" class="form-label">Mensaje</label>
                                    <textarea class="form-control" id="mensaje" rows="6"
                                              placeholder="Escriba su mensaje aquí..." required></textarea>
                                </div>

                                <button type="button" onclick="generarEnlacePersonalizado()" class="btn btn-primary">
                                    <i class="bi bi-link-45deg"></i>
                                    Generar Enlace
                                </button>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Vista Previa del Enlace</label>
                                <div id="enlaceGenerado" class="message-preview" style="min-height: 200px;">
                                    <p class="text-muted">Complete los campos para generar el enlace</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Formatear número de teléfono
        document.getElementById('telefono').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9]/g, '');
            if (value.length > 10) {
                value = value.slice(0, 10);
            }
            e.target.value = value;
        });

        // Generar enlace personalizado
        function generarEnlacePersonalizado() {
            const telefono = document.getElementById('telefono').value;
            const mensaje = document.getElementById('mensaje').value;

            if (!telefono || !mensaje) {
                alert('Por favor complete todos los campos');
                return;
            }

            // Formatear teléfono
            let telefonoFormateado = telefono.replace(/[^0-9]/g, '');
            if (telefonoFormateado.length === 10 && (telefonoFormateado[0] === '8' || telefonoFormateado[0] === '9')) {
                telefonoFormateado = '1' + telefonoFormateado;
            }

            // Generar enlace
            const mensajeCodificado = encodeURIComponent(mensaje);
            const enlace = `https://wa.me/${telefonoFormateado}?text=${mensajeCodificado}`;

            // Mostrar enlace
            const contenedor = document.getElementById('enlaceGenerado');
            contenedor.innerHTML = `
                <p><strong>Enlace generado:</strong></p>
                <a href="${enlace}" target="_blank" class="btn btn-whatsapp w-100 mb-3">
                    <i class="bi bi-whatsapp"></i>
                    Abrir WhatsApp y Enviar Mensaje
                </a>
                <p><strong>URL:</strong></p>
                <small style="word-break: break-all;">${enlace}</small>
            `;
        }

        // Auto-refresh cada 5 minutos
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
