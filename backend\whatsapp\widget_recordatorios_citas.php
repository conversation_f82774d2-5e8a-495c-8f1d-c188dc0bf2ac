<?php
/**
 * Widget de Recordatorios de Citas para Panel Principal
 * Componente para mostrar resumen de citas y recordatorios en el dashboard
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/whatsapp_manager.php';

class WidgetRecordatoriosCitas {
    
    private $pdo;
    private $whatsapp;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->whatsapp = new WhatsAppManager();
    }
    
    /**
     * Obtener estadísticas de citas para hoy y mañana
     */
    public function obtenerEstadisticasCitas() {
        try {
            $stmt = $this->pdo->query("
                SELECT 
                    COUNT(CASE WHEN c.FECHACON = CURDATE() THEN 1 END) as citas_hoy,
                    COUNT(CASE WHEN c.FECHACON = DATE_ADD(CURDATE(), INTERVAL 1 DAY) THEN 1 END) as citas_manana,
                    COUNT(CASE WHEN c.FECHACON BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as citas_semana,
                    COUNT(CASE WHEN c.FECHACON = CURDATE() AND (p.TELEFONO IS NOT NULL OR p.CELULAR IS NOT NULL) THEN 1 END) as hoy_con_telefono,
                    COUNT(CASE WHEN c.FECHACON = DATE_ADD(CURDATE(), INTERVAL 1 DAY) AND (p.TELEFONO IS NOT NULL OR p.CELULAR IS NOT NULL) THEN 1 END) as manana_con_telefono
                FROM CITAMEDIC c
                LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE OR c.NSS = p.CEDULA
                WHERE c.FECHACON BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
                AND c.ESTATUS IN (1, 3, 6)
            ");
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [
                'citas_hoy' => 0,
                'citas_manana' => 0,
                'citas_semana' => 0,
                'hoy_con_telefono' => 0,
                'manana_con_telefono' => 0
            ];
        }
    }
    
    /**
     * Obtener citas urgentes (hoy y mañana)
     */
    public function obtenerCitasUrgentes() {
        try {
            $stmt = $this->pdo->query("
                SELECT 
                    c.*,
                    p.NOMBREAPELLIDO as paciente_nombre,
                    p.TELEFONO as paciente_telefono,
                    p.CELULAR as paciente_celular,
                    DATEDIFF(c.FECHACON, CURDATE()) as dias_hasta_cita
                FROM CITAMEDIC c
                LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE OR c.NSS = p.CEDULA
                WHERE c.FECHACON BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 DAY)
                AND c.ESTATUS IN (1, 3, 6)
                ORDER BY c.FECHACON ASC, c.HORACON ASC
                LIMIT 5
            ");
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Obtener estadísticas de recordatorios enviados
     */
    public function obtenerEstadisticasRecordatorios() {
        try {
            $stmt = $this->pdo->query("
                SELECT 
                    COUNT(CASE WHEN DATE(FECHA_ENVIO) = CURDATE() THEN 1 END) as enviados_hoy,
                    COUNT(CASE WHEN FECHA_ENVIO >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as enviados_semana,
                    COUNT(CASE WHEN TIPO_RECORDATORIO = 'AUTOMATICO' THEN 1 END) as automaticos_total
                FROM RECORDATORIOS_CITAS_WHATSAPP
            ");
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [
                'enviados_hoy' => 0,
                'enviados_semana' => 0,
                'automaticos_total' => 0
            ];
        }
    }
    
    /**
     * Renderizar widget completo
     */
    public function renderWidget() {
        $stats_citas = $this->obtenerEstadisticasCitas();
        $citas_urgentes = $this->obtenerCitasUrgentes();
        $stats_recordatorios = $this->obtenerEstadisticasRecordatorios();
        
        return $this->generarHTML($stats_citas, $citas_urgentes, $stats_recordatorios);
    }
    
    /**
     * Generar HTML del widget
     */
    private function generarHTML($stats_citas, $citas_urgentes, $stats_recordatorios) {
        $html = '
        <div class="card border-success mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-whatsapp"></i>
                    Recordatorios de Citas WhatsApp
                </h5>
            </div>
            <div class="card-body">
                <!-- Estadísticas rápidas -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger mb-1">' . $stats_citas['citas_hoy'] . '</h4>
                            <small class="text-muted">Citas Hoy</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning mb-1">' . $stats_citas['citas_manana'] . '</h4>
                            <small class="text-muted">Citas Mañana</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success mb-1">' . $stats_recordatorios['enviados_hoy'] . '</h4>
                            <small class="text-muted">Enviados Hoy</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info mb-1">' . $stats_citas['citas_semana'] . '</h4>
                            <small class="text-muted">Esta Semana</small>
                        </div>
                    </div>
                </div>
                
                <hr>';
        
        // Citas urgentes
        if (!empty($citas_urgentes)) {
            $html .= '
                <h6><i class="bi bi-exclamation-triangle text-warning"></i> Citas Urgentes</h6>
                <div class="list-group list-group-flush mb-3">';
            
            foreach ($citas_urgentes as $cita) {
                $telefono = !empty($cita['paciente_celular']) ? $cita['paciente_celular'] : $cita['paciente_telefono'];
                $tiene_telefono = !empty($telefono);
                $clase_urgencia = $cita['dias_hasta_cita'] == 0 ? 'border-danger' : 'border-warning';
                $etiqueta_tiempo = $cita['dias_hasta_cita'] == 0 ? 'HOY' : 'MAÑANA';
                $clase_tiempo = $cita['dias_hasta_cita'] == 0 ? 'bg-danger' : 'bg-warning';
                
                $html .= '
                    <div class="list-group-item ' . $clase_urgencia . ' d-flex justify-content-between align-items-center">
                        <div>
                            <strong>' . htmlspecialchars($cita['paciente_nombre'] ?: $cita['NOMBRES']) . '</strong>
                            <br>
                            <small class="text-muted">
                                ' . date('h:i A', strtotime($cita['HORACON'])) . '
                                ' . ($tiene_telefono ? '<i class="bi bi-whatsapp text-success ms-2"></i>' : '<i class="bi bi-telephone-x text-muted ms-2"></i>') . '
                            </small>
                        </div>
                        <div class="text-end">
                            <span class="badge ' . $clase_tiempo . ' text-white">' . $etiqueta_tiempo . '</span>';
                
                if ($tiene_telefono) {
                    $mensaje = $this->generarMensajeRapido($cita);
                    $url_whatsapp = $this->whatsapp->generateWhatsAppURL($telefono, $mensaje);
                    
                    $html .= '
                            <br>
                            <a href="' . htmlspecialchars($url_whatsapp) . '" target="_blank" class="btn btn-sm btn-success mt-1">
                                <i class="bi bi-whatsapp"></i>
                            </a>';
                }
                
                $html .= '
                        </div>
                    </div>';
            }
            
            $html .= '</div>';
        } else {
            $html .= '
                <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle"></i>
                    No hay citas urgentes para hoy o mañana.
                </div>';
        }
        
        // Botones de acción
        $html .= '
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <a href="backend/whatsapp/recordatorios_citas.php" class="btn btn-success btn-sm">
                        <i class="bi bi-whatsapp"></i> Ver Todos los Recordatorios
                    </a>
                    <a href="backend/whatsapp/recordatorios_automaticos.php" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-robot"></i> Configurar Automáticos
                    </a>
                </div>
            </div>
        </div>';
        
        return $html;
    }
    
    /**
     * Generar mensaje rápido para recordatorio
     */
    private function generarMensajeRapido($cita) {
        $dias = $cita['dias_hasta_cita'];
        $fecha_cita = date('d/m/Y', strtotime($cita['FECHACON']));
        $hora_cita = date('h:i A', strtotime($cita['HORACON']));
        
        if ($dias == 0) {
            $cuando = "HOY";
            $emoji = "🚨";
        } else {
            $cuando = "MAÑANA";
            $emoji = "⏰";
        }
        
        $mensaje = "{$emoji} *RECORDATORIO DE CITA*\n\n";
        $mensaje .= "Estimado/a *{$cita['paciente_nombre']}*,\n\n";
        $mensaje .= "Su cita médica es *{$cuando}* a las *{$hora_cita}*\n\n";
        $mensaje .= "Por favor llegue 15 minutos antes.\n";
        $mensaje .= "Traiga su cédula y tarjeta de seguro.\n\n";
        $mensaje .= "🏥 *Consultorio Médico*";
        
        return $mensaje;
    }
    
    /**
     * Renderizar widget compacto para sidebar
     */
    public function renderWidgetCompacto() {
        $stats_citas = $this->obtenerEstadisticasCitas();
        
        return '
        <div class="card border-success mb-3">
            <div class="card-body text-center">
                <h6 class="card-title">
                    <i class="bi bi-whatsapp text-success"></i>
                    Recordatorios
                </h6>
                <div class="row">
                    <div class="col-6">
                        <h5 class="text-danger mb-0">' . $stats_citas['citas_hoy'] . '</h5>
                        <small class="text-muted">Hoy</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning mb-0">' . $stats_citas['citas_manana'] . '</h5>
                        <small class="text-muted">Mañana</small>
                    </div>
                </div>
                <a href="backend/whatsapp/recordatorios_citas.php" class="btn btn-success btn-sm mt-2">
                    <i class="bi bi-whatsapp"></i> Gestionar
                </a>
            </div>
        </div>';
    }
}

// Función helper para incluir el widget fácilmente
function incluir_widget_recordatorios_citas($pdo, $compacto = false) {
    $widget = new WidgetRecordatoriosCitas($pdo);
    
    if ($compacto) {
        echo $widget->renderWidgetCompacto();
    } else {
        echo $widget->renderWidget();
    }
}

// Función para obtener solo estadísticas (para AJAX)
function obtener_estadisticas_citas_json($pdo) {
    $widget = new WidgetRecordatoriosCitas($pdo);
    $stats_citas = $widget->obtenerEstadisticasCitas();
    $stats_recordatorios = $widget->obtenerEstadisticasRecordatorios();
    
    return json_encode([
        'citas' => $stats_citas,
        'recordatorios' => $stats_recordatorios,
        'timestamp' => time()
    ]);
}
?>
