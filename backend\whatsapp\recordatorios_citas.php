<?php
/**
 * Sistema de Recordatorios de Citas por WhatsApp - MarcSoftware Solutions
 * Gestión automática de recordatorios de citas médicas
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], ['admin', 'doctor', 'secretaria'])) {
    header('Location: ../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/whatsapp_manager.php';
require_once __DIR__ . '/config_whatsapp.php';

$whatsapp = new WhatsAppManager();

// Variables para mensajes
$success_message = '';
$error_message = '';
$warning_message = '';

// Obtener citas próximas (próximos 7 días)
try {
    $stmt = $pdo->query("
        SELECT 
            c.*,
            p.NOMBREAPELLIDO as paciente_nombre,
            p.TELEFONO as paciente_telefono,
            p.CELULAR as paciente_celular,
            p.ECORREO as paciente_email,
            p.CEDULA as paciente_cedula,
            DATEDIFF(c.FECHACON, CURDATE()) as dias_hasta_cita,
            CASE 
                WHEN c.ESTATUS = 1 THEN 'Confirmada'
                WHEN c.ESTATUS = 2 THEN 'Cancelada'
                WHEN c.ESTATUS = 3 THEN 'Pendiente'
                WHEN c.ESTATUS = 6 THEN 'Pendiente Aprobación'
                ELSE 'Desconocido'
            END as estado_texto
        FROM CITAMEDIC c
        LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE OR c.NSS = p.CEDULA
        WHERE c.FECHACON BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        AND c.ESTATUS IN (1, 3, 6)
        ORDER BY c.FECHACON ASC, c.HORACON ASC
    ");
    $citas_proximas = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $citas_proximas = [];
    $error_message = "Error al cargar citas: " . $e->getMessage();
}

// Procesar envío de recordatorio
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enviar_recordatorio'])) {
    $cita_id = $_POST['cita_id'];
    
    // Buscar la cita específica
    $stmt = $pdo->prepare("
        SELECT 
            c.*,
            p.NOMBREAPELLIDO as paciente_nombre,
            p.TELEFONO as paciente_telefono,
            p.CELULAR as paciente_celular,
            p.ECORREO as paciente_email,
            p.CEDULA as paciente_cedula,
            DATEDIFF(c.FECHACON, CURDATE()) as dias_hasta_cita
        FROM CITAMEDIC c
        LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE OR c.NSS = p.CEDULA
        WHERE c.CLAVE = ?
    ");
    $stmt->execute([$cita_id]);
    $cita = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($cita) {
        // Determinar el mejor teléfono
        $telefono = !empty($cita['paciente_celular']) ? $cita['paciente_celular'] : $cita['paciente_telefono'];
        
        if (!empty($telefono)) {
            // Generar mensaje de recordatorio
            $mensaje = generarMensajeRecordatorioCita($cita);
            
            // Registrar el recordatorio en la base de datos
            try {
                // Crear tabla si no existe
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS RECORDATORIOS_CITAS_WHATSAPP (
                        ID INT AUTO_INCREMENT PRIMARY KEY,
                        CITA_ID INT NOT NULL,
                        PACIENTE_TELEFONO VARCHAR(20),
                        MENSAJE TEXT,
                        FECHA_ENVIO DATETIME DEFAULT CURRENT_TIMESTAMP,
                        USUARIO_ENVIO VARCHAR(100),
                        TIPO_RECORDATORIO VARCHAR(20) DEFAULT 'MANUAL',
                        ESTADO VARCHAR(20) DEFAULT 'ENVIADO',
                        DIAS_ANTICIPACION INT,
                        INDEX idx_cita (CITA_ID),
                        INDEX idx_fecha (FECHA_ENVIO)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                ");
                
                $stmt_log = $pdo->prepare("
                    INSERT INTO RECORDATORIOS_CITAS_WHATSAPP 
                    (CITA_ID, PACIENTE_TELEFONO, MENSAJE, USUARIO_ENVIO, DIAS_ANTICIPACION)
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                $stmt_log->execute([
                    $cita_id,
                    $telefono,
                    $mensaje,
                    $_SESSION['usuario'],
                    $cita['dias_hasta_cita']
                ]);
                
                $success_message = "Recordatorio preparado para envío";
                $whatsapp_url = $whatsapp->generateWhatsAppURL($telefono, $mensaje);
                $show_whatsapp = true;
                
            } catch (Exception $e) {
                $error_message = "Error al registrar recordatorio: " . $e->getMessage();
            }
        } else {
            $error_message = "El paciente no tiene número de teléfono registrado";
        }
    }
}

// Función para generar mensaje de recordatorio de cita
function generarMensajeRecordatorioCita($cita) {
    $dias = $cita['dias_hasta_cita'];
    $fecha_cita = date('d/m/Y', strtotime($cita['FECHACON']));
    $hora_cita = date('h:i A', strtotime($cita['HORACON']));
    
    if ($dias == 0) {
        $cuando = "HOY";
        $emoji = "🚨";
    } elseif ($dias == 1) {
        $cuando = "MAÑANA";
        $emoji = "⏰";
    } else {
        $cuando = "en {$dias} días";
        $emoji = "📅";
    }
    
    $mensaje = "{$emoji} *RECORDATORIO DE CITA MÉDICA*\n\n";
    $mensaje .= "Estimado/a *{$cita['paciente_nombre']}*,\n\n";
    $mensaje .= "Le recordamos que tiene una cita médica programada para *{$cuando}*:\n\n";
    $mensaje .= "📅 *Fecha:* {$fecha_cita}\n";
    $mensaje .= "🕐 *Hora:* {$hora_cita}\n";
    
    if (!empty($cita['CONSULTORIO'])) {
        $mensaje .= "🏥 *Consultorio:* {$cita['CONSULTORIO']}\n";
    }
    
    if (!empty($cita['OBSERVACION'])) {
        $mensaje .= "📝 *Observaciones:* {$cita['OBSERVACION']}\n";
    }
    
    $mensaje .= "\n📋 *Recomendaciones:*\n";
    $mensaje .= "• Llegue 15 minutos antes de su cita\n";
    $mensaje .= "• Traiga su cédula de identidad\n";
    $mensaje .= "• Traiga su tarjeta de seguro (si aplica)\n";
    $mensaje .= "• Si tiene estudios previos, tráigalos\n\n";
    
    if ($dias <= 1) {
        $mensaje .= "⚠️ *IMPORTANTE:* Si no puede asistir, por favor comuníquese con nosotros lo antes posible.\n\n";
    } else {
        $mensaje .= "Si necesita reprogramar su cita, contáctenos con anticipación.\n\n";
    }
    
    $mensaje .= "📞 *Contacto:* " . WHATSAPP_SOPORTE . "\n";
    $mensaje .= "🏥 *Consultorio Médico*\n";
    $mensaje .= "Cuidando su salud con profesionalismo ❤️";
    
    return $mensaje;
}

// Obtener estadísticas
$stats = [
    'hoy' => count(array_filter($citas_proximas, fn($c) => $c['dias_hasta_cita'] == 0)),
    'manana' => count(array_filter($citas_proximas, fn($c) => $c['dias_hasta_cita'] == 1)),
    'esta_semana' => count(array_filter($citas_proximas, fn($c) => $c['dias_hasta_cita'] <= 7)),
    'con_telefono' => count(array_filter($citas_proximas, fn($c) => !empty($c['paciente_telefono']) || !empty($c['paciente_celular'])))
];
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recordatorios de Citas WhatsApp - Consultorio Médico</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <style>
        .cita-hoy { background-color: #fff5f5; border-left: 4px solid #dc3545; }
        .cita-manana { background-color: #fff8e1; border-left: 4px solid #ffc107; }
        .cita-proxima { background-color: #f0f8ff; border-left: 4px solid #0dcaf0; }
        .whatsapp-btn { background-color: #25d366; border-color: #25d366; }
        .whatsapp-btn:hover { background-color: #128c7e; border-color: #128c7e; }
        .sin-telefono { opacity: 0.6; }
        .stat-card { transition: transform 0.2s; }
        .stat-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-whatsapp text-success"></i>
                    Recordatorios de Citas - WhatsApp
                </h1>
                
                <?php if (isset($success_message) && $success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="bi bi-check-circle"></i> <?= $success_message ?>
                        <?php if (isset($show_whatsapp) && $show_whatsapp): ?>
                            <hr>
                            <div class="d-grid gap-2 d-md-flex">
                                <a href="<?= htmlspecialchars($whatsapp_url) ?>" target="_blank" class="btn whatsapp-btn text-white">
                                    <i class="bi bi-whatsapp"></i> Abrir WhatsApp
                                </a>
                                <button type="button" class="btn btn-outline-success" onclick="copyMessage()">
                                    <i class="bi bi-clipboard"></i> Copiar Mensaje
                                </button>
                            </div>
                            <script>
                                const mensaje = `<?= addslashes($mensaje ?? '') ?>`;
                                function copyMessage() {
                                    navigator.clipboard.writeText(mensaje).then(() => {
                                        alert('✅ Mensaje copiado al portapapeles');
                                    });
                                }
                            </script>
                        <?php endif; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="bi bi-exclamation-triangle"></i> <?= $error_message ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Estadísticas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center stat-card border-danger">
                            <div class="card-body">
                                <h5 class="card-title text-danger"><?= $stats['hoy'] ?></h5>
                                <p class="card-text">Citas Hoy</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center stat-card border-warning">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?= $stats['manana'] ?></h5>
                                <p class="card-text">Citas Mañana</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center stat-card border-info">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?= $stats['esta_semana'] ?></h5>
                                <p class="card-text">Esta Semana</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center stat-card border-success">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?= $stats['con_telefono'] ?></h5>
                                <p class="card-text">Con WhatsApp</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Lista de Citas Próximas -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-calendar-check"></i> Citas Próximas (Próximos 7 días)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($citas_proximas)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                No hay citas programadas para los próximos 7 días.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Paciente</th>
                                            <th>Fecha</th>
                                            <th>Hora</th>
                                            <th>Estado</th>
                                            <th>Teléfono</th>
                                            <th>Días</th>
                                            <th>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($citas_proximas as $cita): ?>
                                            <?php 
                                            $telefono = !empty($cita['paciente_celular']) ? $cita['paciente_celular'] : $cita['paciente_telefono'];
                                            $clase_fila = '';
                                            if ($cita['dias_hasta_cita'] == 0) $clase_fila = 'cita-hoy';
                                            elseif ($cita['dias_hasta_cita'] == 1) $clase_fila = 'cita-manana';
                                            else $clase_fila = 'cita-proxima';
                                            
                                            if (empty($telefono)) $clase_fila .= ' sin-telefono';
                                            ?>
                                            <tr class="<?= $clase_fila ?>">
                                                <td>
                                                    <strong><?= htmlspecialchars($cita['paciente_nombre'] ?: $cita['NOMBRES']) ?></strong>
                                                    <?php if (!empty($cita['paciente_cedula'])): ?>
                                                        <br><small class="text-muted">Cédula: <?= htmlspecialchars($cita['paciente_cedula']) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= date('d/m/Y', strtotime($cita['FECHACON'])) ?></td>
                                                <td><?= date('h:i A', strtotime($cita['HORACON'])) ?></td>
                                                <td>
                                                    <span class="badge bg-<?= $cita['ESTATUS'] == 1 ? 'success' : ($cita['ESTATUS'] == 6 ? 'warning' : 'secondary') ?>">
                                                        <?= $cita['estado_texto'] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($telefono)): ?>
                                                        <i class="bi bi-whatsapp text-success"></i>
                                                        <?= htmlspecialchars($telefono) ?>
                                                        <?php if (!empty($cita['paciente_celular']) && !empty($cita['paciente_telefono'])): ?>
                                                            <br><small class="text-muted">Tel: <?= htmlspecialchars($cita['paciente_telefono']) ?></small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <small class="text-muted">No disponible</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($cita['dias_hasta_cita'] == 0): ?>
                                                        <span class="badge bg-danger">HOY</span>
                                                    <?php elseif ($cita['dias_hasta_cita'] == 1): ?>
                                                        <span class="badge bg-warning">MAÑANA</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-info"><?= $cita['dias_hasta_cita'] ?> días</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($telefono)): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="cita_id" value="<?= $cita['CLAVE'] ?>">
                                                            <button type="submit" name="enviar_recordatorio" class="btn btn-sm whatsapp-btn text-white">
                                                                <i class="bi bi-whatsapp"></i> Recordatorio
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-sm btn-outline-secondary" disabled>
                                                            <i class="bi bi-telephone-x"></i> Sin teléfono
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Botones de acción -->
                <div class="mt-4">
                    <a href="recordatorios_automaticos.php" class="btn btn-primary">
                        <i class="bi bi-robot"></i> Configurar Recordatorios Automáticos
                    </a>
                    <a href="../citas/gestionar_citas.php" class="btn btn-secondary">
                        <i class="bi bi-calendar"></i> Gestionar Citas
                    </a>
                    <a href="../../index.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Inicio
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
