<?php
/**
 * Widget de WhatsApp - MarcSoftware Solutions
 * Componente reutilizable para integrar WhatsApp en diferentes páginas
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

require_once __DIR__ . '/whatsapp_manager.php';

class WhatsAppWidget {
    
    private $whatsapp_manager;
    private $default_phone = '18095551234'; // Número por defecto de soporte
    
    public function __construct() {
        $this->whatsapp_manager = new WhatsAppManager();
    }
    
    /**
     * Renderizar botón flotante de WhatsApp
     */
    public function renderFloatingButton($phone = null, $message = null) {
        $phone = $phone ?: $this->default_phone;
        $message = $message ?: "Hola, necesito información sobre los servicios de MarcSoftware Solutions.";
        
        $whatsapp_url = $this->whatsapp_manager->generateWhatsAppURL($phone, $message);
        
        return '
        <div id="whatsapp-float" class="whatsapp-float">
            <a href="' . htmlspecialchars($whatsapp_url) . '" target="_blank" class="whatsapp-button">
                <i class="bi bi-whatsapp"></i>
                <span class="whatsapp-tooltip">¿Necesitas ayuda? Escríbenos</span>
            </a>
        </div>
        
        <style>
            .whatsapp-float {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
            }
            
            .whatsapp-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 60px;
                height: 60px;
                background: #25d366;
                border-radius: 50%;
                color: white;
                text-decoration: none;
                font-size: 24px;
                box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
                transition: all 0.3s ease;
                position: relative;
            }
            
            .whatsapp-button:hover {
                background: #128c7e;
                transform: scale(1.1);
                color: white;
                text-decoration: none;
            }
            
            .whatsapp-tooltip {
                position: absolute;
                right: 70px;
                top: 50%;
                transform: translateY(-50%);
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 14px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .whatsapp-tooltip::after {
                content: "";
                position: absolute;
                left: 100%;
                top: 50%;
                transform: translateY(-50%);
                border: 6px solid transparent;
                border-left-color: #333;
            }
            
            .whatsapp-button:hover .whatsapp-tooltip {
                opacity: 1;
                visibility: visible;
            }
            
            @media (max-width: 768px) {
                .whatsapp-float {
                    bottom: 15px;
                    right: 15px;
                }
                
                .whatsapp-button {
                    width: 50px;
                    height: 50px;
                    font-size: 20px;
                }
                
                .whatsapp-tooltip {
                    display: none;
                }
            }
        </style>';
    }
    
    /**
     * Renderizar sección de contacto WhatsApp
     */
    public function renderContactSection($title = "Contacto WhatsApp", $subtitle = "¿Necesitas ayuda? Estamos aquí para ti") {
        return '
        <div class="whatsapp-contact-section">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-whatsapp"></i> ' . htmlspecialchars($title) . '
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">' . htmlspecialchars($subtitle) . '</p>
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="https://wa.me/' . $this->default_phone . '?text=' . urlencode("Hola, necesito información sobre MarcSoftware Solutions") . '" 
                           target="_blank" class="btn btn-success">
                            <i class="bi bi-whatsapp"></i> Chatear Ahora
                        </a>
                        <a href="https://wa.me/' . $this->default_phone . '?text=' . urlencode("Necesito soporte técnico urgente") . '" 
                           target="_blank" class="btn btn-outline-success">
                            <i class="bi bi-tools"></i> Soporte Técnico
                        </a>
                    </div>
                    <small class="text-muted d-block mt-2">
                        <i class="bi bi-clock"></i> Horario de atención: Lunes a Viernes 8:00 AM - 6:00 PM
                    </small>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Renderizar botones de WhatsApp para facturas
     */
    public function renderInvoiceButtons($factura_data, $cliente_telefono = null) {
        if (!$cliente_telefono) {
            return '<div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        No se encontró número de teléfono para envío por WhatsApp
                    </div>';
        }
        
        $mensaje_factura = $this->whatsapp_manager->generateInvoiceMessage($factura_data);
        $whatsapp_url = $this->whatsapp_manager->generateWhatsAppURL($cliente_telefono, $mensaje_factura);
        
        return '
        <div class="whatsapp-invoice-buttons">
            <div class="alert alert-info">
                <h6><i class="bi bi-whatsapp"></i> Enviar Factura por WhatsApp</h6>
                <div class="d-grid gap-2 d-md-flex">
                    <a href="' . htmlspecialchars($whatsapp_url) . '" target="_blank" class="btn btn-success">
                        <i class="bi bi-whatsapp"></i> Enviar al Cliente
                    </a>
                    <button type="button" class="btn btn-outline-success" onclick="copyInvoiceMessage()">
                        <i class="bi bi-clipboard"></i> Copiar Mensaje
                    </button>
                </div>
                <small class="text-muted">Teléfono: ' . htmlspecialchars($cliente_telefono) . '</small>
            </div>
            
            <script>
                function copyInvoiceMessage() {
                    const mensaje = `' . addslashes($mensaje_factura) . '`;
                    navigator.clipboard.writeText(mensaje).then(() => {
                        alert("✅ Mensaje copiado al portapapeles");
                    });
                }
            </script>
        </div>';
    }
    
    /**
     * Renderizar estadísticas de WhatsApp
     */
    public function renderStats($pdo) {
        try {
            // Obtener estadísticas de recordatorios
            $stmt = $pdo->query("
                SELECT 
                    COUNT(*) as total_recordatorios,
                    COUNT(CASE WHEN DATE(FECHA_ENVIO) = CURDATE() THEN 1 END) as hoy,
                    COUNT(CASE WHEN FECHA_ENVIO >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as esta_semana
                FROM RECORDATORIOS_WHATSAPP
            ");
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return '
            <div class="row">
                <div class="col-md-4">
                    <div class="card text-center border-success">
                        <div class="card-body">
                            <h5 class="card-title text-success">' . ($stats['total_recordatorios'] ?? 0) . '</h5>
                            <p class="card-text">Total Mensajes</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center border-info">
                        <div class="card-body">
                            <h5 class="card-title text-info">' . ($stats['hoy'] ?? 0) . '</h5>
                            <p class="card-text">Enviados Hoy</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center border-warning">
                        <div class="card-body">
                            <h5 class="card-title text-warning">' . ($stats['esta_semana'] ?? 0) . '</h5>
                            <p class="card-text">Esta Semana</p>
                        </div>
                    </div>
                </div>
            </div>';
            
        } catch (Exception $e) {
            return '<div class="alert alert-warning">No se pudieron cargar las estadísticas de WhatsApp</div>';
        }
    }
    
    /**
     * Renderizar formulario de mensaje personalizado
     */
    public function renderCustomMessageForm() {
        return '
        <div class="whatsapp-custom-form">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-chat-text"></i> Enviar Mensaje Personalizado</h5>
                </div>
                <div class="card-body">
                    <form id="whatsapp-custom-form">
                        <div class="mb-3">
                            <label for="cliente-telefono" class="form-label">Teléfono del Cliente</label>
                            <input type="tel" class="form-control" id="cliente-telefono" 
                                   placeholder="Ej: ************" required>
                        </div>
                        <div class="mb-3">
                            <label for="mensaje-personalizado" class="form-label">Mensaje</label>
                            <textarea class="form-control" id="mensaje-personalizado" rows="4" 
                                      placeholder="Escriba su mensaje aquí..." required></textarea>
                        </div>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="button" class="btn btn-success" onclick="enviarMensajePersonalizado()">
                                <i class="bi bi-whatsapp"></i> Enviar por WhatsApp
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="copiarMensajePersonalizado()">
                                <i class="bi bi-clipboard"></i> Copiar Mensaje
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <script>
            function enviarMensajePersonalizado() {
                const telefono = document.getElementById("cliente-telefono").value;
                const mensaje = document.getElementById("mensaje-personalizado").value;
                
                if (!telefono || !mensaje) {
                    alert("Por favor complete todos los campos");
                    return;
                }
                
                // Limpiar teléfono
                const telefonoLimpio = telefono.replace(/[^0-9]/g, "");
                let telefonoFormateado = telefonoLimpio;
                
                // Agregar código de país si es necesario
                if (telefonoLimpio.length === 10) {
                    telefonoFormateado = "1809" + telefonoLimpio;
                } else if (telefonoLimpio.length === 7) {
                    telefonoFormateado = "1809" + telefonoLimpio;
                }
                
                const url = `https://wa.me/${telefonoFormateado}?text=${encodeURIComponent(mensaje)}`;
                window.open(url, "_blank");
            }
            
            function copiarMensajePersonalizado() {
                const mensaje = document.getElementById("mensaje-personalizado").value;
                if (!mensaje) {
                    alert("No hay mensaje para copiar");
                    return;
                }
                
                navigator.clipboard.writeText(mensaje).then(() => {
                    alert("✅ Mensaje copiado al portapapeles");
                });
            }
        </script>';
    }
    
    /**
     * Obtener CSS necesario para los widgets
     */
    public static function getCSS() {
        return '
        <style>
            .whatsapp-contact-section .card {
                transition: transform 0.2s ease;
            }
            
            .whatsapp-contact-section .card:hover {
                transform: translateY(-2px);
            }
            
            .whatsapp-invoice-buttons .btn {
                transition: all 0.2s ease;
            }
            
            .whatsapp-invoice-buttons .btn:hover {
                transform: translateY(-1px);
            }
            
            .whatsapp-custom-form textarea {
                resize: vertical;
                min-height: 100px;
            }
        </style>';
    }
}

// Función helper para incluir el widget fácilmente
function incluir_whatsapp_flotante($telefono = null, $mensaje = null) {
    $widget = new WhatsAppWidget();
    echo $widget->renderFloatingButton($telefono, $mensaje);
    echo WhatsAppWidget::getCSS();
}

function incluir_seccion_contacto_whatsapp($titulo = null, $subtitulo = null) {
    $widget = new WhatsAppWidget();
    echo $widget->renderContactSection($titulo, $subtitulo);
}
?>
