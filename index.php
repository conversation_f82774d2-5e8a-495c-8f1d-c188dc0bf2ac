<?php
/**
 * Panel Principal del Sistema de Consultorio Médico
 * Versión moderna con dashboard interactivo
 *
 * <AUTHOR> de Consultorio
 * @version 2.0
 * @date 2024
 */

session_start();

// Configuración de seguridad
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: login.php');
    exit;
}

// Redirección específica por rol
if ($_SESSION['rol'] === 'doctor') {
    header('Location: doctor_panel_v2.php');
    exit;
}

// Configuración de la aplicación
require_once __DIR__ . '/backend/config/database.php';
date_default_timezone_set('America/Santo_Domingo');

// Obtener información de la empresa
try {
    $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
    $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $empresa = null;
}

// Formatear fecha actual
$date = new DateTime();
$formatter = new IntlDateFormatter(
    'es_ES',
    IntlDateFormatter::FULL,
    IntlDateFormatter::NONE
);
$formatter->setPattern("EEEE, dd 'de' MMMM 'de' yyyy");
$fechaActual = $formatter->format($date);

// Datos del usuario
$usuario = htmlspecialchars($_SESSION['usuario']);
$rol = htmlspecialchars($_SESSION['rol']);

// Obtener estadísticas rápidas para el dashboard
try {
    // Citas de hoy
    $stmt_citas_hoy = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE()");
    $citas_hoy = $stmt_citas_hoy->fetch(PDO::FETCH_ASSOC)['total'];

    // Pacientes registrados
    $stmt_pacientes = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
    $total_pacientes = $stmt_pacientes->fetch(PDO::FETCH_ASSOC)['total'];

    // Citas pendientes
    $stmt_pendientes = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON >= CURDATE() AND ESTATUS = 3");
    $citas_pendientes = $stmt_pendientes->fetch(PDO::FETCH_ASSOC)['total'];

    // Citas atendidas hoy
    $stmt_atendidas = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 0");
    $citas_atendidas = $stmt_atendidas->fetch(PDO::FETCH_ASSOC)['total'];

} catch (Exception $e) {
    $citas_hoy = 0;
    $total_pacientes = 0;
    $citas_pendientes = 0;
    $citas_atendidas = 0;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Sistema de Consultorio'; ?></title>

    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js para gráficos -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            /* Paleta médica profesional */
            --medical-primary: #1e40af;        /* Azul médico profundo */
            --medical-secondary: #3b82f6;      /* Azul médico medio */
            --medical-light: #dbeafe;          /* Azul muy claro */
            --medical-accent: #0ea5e9;         /* Azul cielo médico */
            --medical-dark: #1e3a8a;           /* Azul marino médico */

            /* Colores de estado médico */
            --success-medical: #10b981;        /* Verde médico (saludable) */
            --warning-medical: #f59e0b;        /* Amarillo médico (precaución) */
            --danger-medical: #ef4444;         /* Rojo médico (urgente) */
            --info-medical: #06b6d4;           /* Cian médico (información) */

            /* Colores neutros médicos */
            --medical-white: #ffffff;
            --medical-gray-50: #f8fafc;
            --medical-gray-100: #f1f5f9;
            --medical-gray-200: #e2e8f0;
            --medical-gray-300: #cbd5e1;
            --medical-gray-400: #94a3b8;
            --medical-gray-500: #64748b;
            --medical-gray-600: #475569;
            --medical-gray-700: #334155;
            --medical-gray-800: #1e293b;
            --medical-gray-900: #0f172a;

            /* Configuración de diseño */
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --box-shadow-medical: 0 10px 25px -5px rgba(30, 64, 175, 0.1), 0 8px 10px -6px rgba(30, 64, 175, 0.1);
            --box-shadow-hover: 0 20px 25px -5px rgba(30, 64, 175, 0.15), 0 10px 10px -5px rgba(30, 64, 175, 0.1);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 25%, #0ea5e9  50%, #06b6d4 75%, #10b981 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: var(--medical-gray-800);
            position: relative;
        }

        /* Patrón médico de fondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            background-size: 100px 100px;
            pointer-events: none;
            z-index: -1;
        }

        /* Navbar médica moderna */
        .navbar-modern {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
            backdrop-filter: blur(15px);
            border-bottom: 2px solid rgba(30, 64, 175, 0.1);
            padding: 1.2rem 0;
            box-shadow: 0 4px 20px rgba(30, 64, 175, 0.08);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.6rem;
            color: var(--medical-primary) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: var(--medical-accent);
            font-size: 1.8rem;
        }

        .nav-link {
            font-weight: 500;
            color: var(--medical-gray-600) !important;
            transition: var(--transition);
            border-radius: 12px;
            padding: 0.75rem 1.25rem !important;
            margin: 0 0.25rem;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--medical-primary) !important;
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
        }

        .nav-link i {
            margin-right: 0.5rem;
            transition: var(--transition);
        }

        .nav-link:hover i {
            transform: scale(1.1);
            color: var(--medical-accent);
        }

        /* Header médico del dashboard */
        .dashboard-header {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.95) 50%,
                rgba(219, 234, 254, 0.9) 100%);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 3rem 2rem;
            margin: 2rem 0;
            box-shadow: var(--box-shadow-medical);
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(30, 64, 175, 0.1);
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                var(--medical-primary) 0%,
                var(--medical-secondary) 25%,
                var(--medical-accent) 50%,
                var(--info-medical) 75%,
                var(--success-medical) 100%);
        }

        .dashboard-header .logo-container {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .dashboard-header .logo-container img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid var(--medical-white);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.2);
            transition: var(--transition);
            object-fit: cover;
        }

        .dashboard-header .logo-container img:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(30, 64, 175, 0.3);
        }

        .dashboard-header h1 {
            font-weight: 700;
            color: var(--medical-primary);
            margin-bottom: 0.5rem;
            font-size: 2.2rem;
            text-shadow: 0 2px 4px rgba(30, 64, 175, 0.1);
        }

        .dashboard-header .subtitle {
            color: var(--medical-gray-600);
            font-size: 1.2rem;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .user-info {
            display: inline-flex;
            align-items: center;
            gap: 1.5rem;
            background: linear-gradient(135deg,
                rgba(30, 64, 175, 0.1) 0%,
                rgba(59, 130, 246, 0.08) 100%);
            padding: 1rem 2rem;
            border-radius: 50px;
            color: var(--medical-primary);
            font-weight: 500;
            border: 1px solid rgba(30, 64, 175, 0.15);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.1);
            transition: var(--transition);
        }

        .user-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.15);
        }

        .user-info i {
            color: var(--medical-accent);
            font-size: 1.1rem;
        }

        .user-info .badge {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: var(--medical-white);
            padding: 0.4rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
        }

        /* Cards médicas de estadísticas */
        .stat-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(30, 64, 175, 0.1);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow-medical);
            transition: var(--transition);
            overflow: hidden;
            height: 100%;
            position: relative;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                var(--medical-primary) 0%,
                var(--medical-accent) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--box-shadow-hover);
            border-color: rgba(30, 64, 175, 0.2);
        }

        .stat-card .card-body {
            padding: 2.5rem 2rem;
            position: relative;
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: var(--medical-white);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .stat-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .stat-card:hover .stat-icon::before {
            left: 100%;
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 800;
            line-height: 1;
            margin-bottom: 0.75rem;
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--medical-gray-600);
            font-weight: 600;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
        }

        .stat-label::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--medical-primary), var(--medical-accent));
            transition: width 0.3s ease;
        }

        .stat-card:hover .stat-label::after {
            width: 100%;
        }

        /* Cards médicas de acciones rápidas */
        .action-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(30, 64, 175, 0.1);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow-medical);
            transition: var(--transition);
            text-decoration: none;
            color: inherit;
            height: 100%;
            overflow: hidden;
            position: relative;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(30, 64, 175, 0.02) 0%,
                rgba(59, 130, 246, 0.01) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-card:hover::before {
            opacity: 1;
        }

        .action-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--box-shadow-hover);
            text-decoration: none;
            color: inherit;
            border-color: rgba(30, 64, 175, 0.2);
        }

        .action-card .card-body {
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .action-icon {
            width: 90px;
            height: 90px;
            border-radius: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            color: var(--medical-white);
            margin: 0 auto 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.2);
        }

        .action-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .action-card:hover .action-icon::after {
            transform: translateX(100%);
        }

        .action-title {
            font-size: 1.35rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--medical-primary);
            transition: var(--transition);
        }

        .action-card:hover .action-title {
            color: var(--medical-accent);
            transform: translateY(-2px);
        }

        .action-description {
            color: var(--medical-gray-600);
            font-size: 0.95rem;
            line-height: 1.6;
            font-weight: 400;
        }

        /* Gradientes médicos profesionales */
        .bg-medical-primary {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
        }
        .bg-medical-success {
            background: linear-gradient(135deg, var(--success-medical) 0%, #34d399 100%);
        }
        .bg-medical-warning {
            background: linear-gradient(135deg, var(--warning-medical) 0%, #fbbf24 100%);
        }
        .bg-medical-info {
            background: linear-gradient(135deg, var(--info-medical) 0%, var(--medical-accent) 100%);
        }
        .bg-medical-danger {
            background: linear-gradient(135deg, var(--danger-medical) 0%, #f87171 100%);
        }

        /* Gradientes específicos para iconos médicos */
        .bg-citas-gradient {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #0ea5e9 100%);
        }
        .bg-atendidas-gradient {
            background: linear-gradient(135deg, #10b981 0%, #34d399 50%, #6ee7b7 100%);
        }
        .bg-pendientes-gradient {
            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fcd34d 100%);
        }
        .bg-pacientes-gradient {
            background: linear-gradient(135deg, #06b6d4 0%, #0ea5e9 50%, #38bdf8 100%);
        }
        .bg-nueva-cita-gradient {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        }
        .bg-gestion-pacientes-gradient {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        }
        .bg-gestion-citas-gradient {
            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
        }
        .bg-historia-gradient {
            background: linear-gradient(135deg, #06b6d4 0%, #0ea5e9 100%);
        }
        .bg-facturacion-gradient {
            background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        }
        .bg-reportes-gradient {
            background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
        }

        /* Footer médico moderno */
        .modern-footer {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(15px);
            border-top: 2px solid rgba(30, 64, 175, 0.1);
            padding: 2.5rem 0;
            margin-top: 4rem;
            box-shadow: 0 -4px 20px rgba(30, 64, 175, 0.08);
        }

        .modern-footer .btn {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .modern-footer .btn-outline-primary {
            border-color: var(--medical-primary);
            color: var(--medical-primary);
        }

        .modern-footer .btn-outline-primary:hover {
            background: var(--medical-primary);
            border-color: var(--medical-primary);
            color: var(--medical-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.2);
        }

        .modern-footer .btn-outline-info {
            border-color: var(--info-medical);
            color: var(--info-medical);
        }

        .modern-footer .btn-outline-info:hover {
            background: var(--info-medical);
            border-color: var(--info-medical);
            color: var(--medical-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.2);
        }

        .modern-footer .btn-outline-danger {
            border-color: var(--danger-medical);
            color: var(--danger-medical);
        }

        .modern-footer .btn-outline-danger:hover {
            background: var(--danger-medical);
            border-color: var(--danger-medical);
            color: var(--medical-white);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-header {
                padding: 1.5rem;
                margin: 1rem 0;
            }

            .stat-card .card-body,
            .action-card .card-body {
                padding: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .action-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
        }

        /* Animaciones */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navegación principal moderna -->
    <nav class="navbar navbar-expand-lg navbar-modern relative-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-stethoscope me-2"></i>
                <?php
                if ($empresa) {
                    echo htmlspecialchars($empresa['NOMBRE']);
                } else {
                    echo "Sistema de Consultorio";
                }
                ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent"
                    aria-controls="navbarContent" aria-expanded="false" aria-label="Alternar navegación">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarContent">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="backend/pacientes/gestion_pacientes.php">
                            <i class="fas fa-users me-1"></i> Pacientes
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="citasDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-calendar-check me-1"></i> Citas
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="backend/citas/crear_cita.php">
                                <i class="fas fa-plus me-2"></i> Crear Cita
                            </a></li>
                            <li><a class="dropdown-item" href="backend/citas/gestion_citas.php">
                                <i class="fas fa-list me-2"></i> Gestionar Citas
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="backend/historia/historia_menu.php">
                            <i class="fas fa-notes-medical me-1"></i> Historia Clínica
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="backend/facturas/facturacion.php">
                            <i class="fas fa-file-invoice-dollar me-1"></i> Facturación
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./reportes.php">
                            <i class="fas fa-chart-line me-1"></i> Reportes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="backend/reportes/admin_sugerencias.php">
                            <i class="fas fa-lightbulb me-1"></i> Sugerencias
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="configuracion.php">
                            <i class="fas fa-cogs me-1"></i> Configuración
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="logout.php">
                            <i class="fas fa-sign-out-alt me-1"></i> Salir
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Contenido principal del dashboard -->
    <main class="container" style="margin-top: 100px;">
        <!-- Header médico del dashboard -->
        <div class="dashboard-header fade-in-up">
            <!-- Logo/Imagen del consultorio -->
            <div class="logo-container">
                <img src="assets/img/consultorio.png" alt="Logo del Consultorio"
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiByeD0iNjAiIGZpbGw9IiMxZTQwYWYiLz4KPHN2ZyB4PSIzMCIgeT0iMzAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xOS4xNCwyMC45NEgxNi43OVYxOC41OUgxOS4xNFYyMC45NFpNMTYuNzksMTYuMjRIMTkuMTRWMTguNTlIMTYuNzlWMTYuMjRaTTE0LjQ0LDE4LjU5SDE2Ljc5VjIwLjk0SDE0LjQ0VjE4LjU5Wk0xNC40NCwxNi4yNEgxNi43OVYxOC41OUgxNC40NFYxNi4yNFpNMTIuMDksMTguNTlIMTQuNDRWMjAuOTRIMTIuMDlWMTguNTlaTTEyLjA5LDE2LjI0SDE0LjQ0VjE4LjU5SDEyLjA5VjE2LjI0Wk05Ljc0LDE4LjU5SDEyLjA5VjIwLjk0SDkuNzRWMTguNTlaTTkuNzQsMTYuMjRIMTIuMDlWMTguNTlIOS43NFYxNi4yNFpNNy4zOSwxOC41OUg5Ljc0VjIwLjk0SDcuMzlWMTguNTlaTTcuMzksMTYuMjRIOS43NFYxOC41OUg3LjM5VjE2LjI0WiIvPgo8L3N2Zz4KPC9zdmc+'">
            </div>

            <h1><?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Sistema de Consultorio Médico'; ?></h1>
            <p class="subtitle">
                <i class="fas fa-heartbeat me-2"></i>
                Panel de control y gestión médica profesional
            </p>

            <div class="user-info">
                <i class="fas fa-user-md"></i>
                <span>Dr. <?php echo $usuario; ?></span>
                <span class="badge"><?php echo ucfirst($rol); ?></span>
                <i class="fas fa-calendar-alt"></i>
                <span><?php echo ucfirst($fechaActual); ?></span>
            </div>
        </div>

        <!-- Estadísticas médicas rápidas -->
        <div class="row g-4 mb-5">
            <div class="col-lg-3 col-md-6">
                <div class="card stat-card fade-in-up" style="animation-delay: 0.1s;">
                    <div class="card-body">
                        <div class="stat-icon bg-citas-gradient">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="stat-number"><?php echo $citas_hoy; ?></div>
                        <div class="stat-label">Citas Hoy</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card stat-card fade-in-up" style="animation-delay: 0.2s;">
                    <div class="card-body">
                        <div class="stat-icon bg-atendidas-gradient">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-number"><?php echo $citas_atendidas; ?></div>
                        <div class="stat-label">Atendidas</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card stat-card fade-in-up" style="animation-delay: 0.3s;">
                    <div class="card-body">
                        <div class="stat-icon bg-pendientes-gradient">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number"><?php echo $citas_pendientes; ?></div>
                        <div class="stat-label">Pendientes</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card stat-card fade-in-up" style="animation-delay: 0.4s;">
                    <div class="card-body">
                        <div class="stat-icon bg-pacientes-gradient">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number"><?php echo $total_pacientes; ?></div>
                        <div class="stat-label">Pacientes</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones médicas rápidas -->
        <div class="row g-4 mb-5">
            <div class="col-lg-4 col-md-6">
                <a href="backend/citas/crear_cita.php" class="action-card card fade-in-up" style="animation-delay: 0.5s;">
                    <div class="card-body">
                        <div class="action-icon bg-nueva-cita-gradient">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <h5 class="action-title">Nueva Cita</h5>
                        <p class="action-description">Agendar una nueva cita médica para un paciente registrado</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-4 col-md-6">
                <a href="backend/pacientes/gestion_pacientes.php" class="action-card card fade-in-up" style="animation-delay: 0.6s;">
                    <div class="card-body">
                        <div class="action-icon bg-gestion-pacientes-gradient">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h5 class="action-title">Gestión de Pacientes</h5>
                        <p class="action-description">Registrar nuevos pacientes y gestionar información médica</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-4 col-md-6">
                <a href="backend/citas/gestion_citas.php" class="action-card card fade-in-up" style="animation-delay: 0.7s;">
                    <div class="card-body">
                        <div class="action-icon bg-gestion-citas-gradient">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h5 class="action-title">Gestionar Citas</h5>
                        <p class="action-description">Ver, editar y administrar todas las citas programadas</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-4 col-md-6">
                <a href="backend/historia/historia_menu.php" class="action-card card fade-in-up" style="animation-delay: 0.8s;">
                    <div class="card-body">
                        <div class="action-icon bg-historia-gradient">
                            <i class="fas fa-notes-medical"></i>
                        </div>
                        <h5 class="action-title">Historia Clínica</h5>
                        <p class="action-description">Consultar y actualizar historiales médicos de pacientes</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-4 col-md-6">
                <a href="backend/facturas/facturacion.php" class="action-card card fade-in-up" style="animation-delay: 0.9s;">
                    <div class="card-body">
                        <div class="action-icon bg-facturacion-gradient">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <h5 class="action-title">Facturación</h5>
                        <p class="action-description">Generar facturas y gestionar pagos de consultas</p>
                    </div>
                </a>
            </div>

            <div class="col-lg-4 col-md-6">
                <a href="./reportes.php" class="action-card card fade-in-up" style="animation-delay: 1.0s;">
                    <div class="card-body">
                        <div class="action-icon bg-reportes-gradient">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5 class="action-title">Reportes Médicos</h5>
                        <p class="action-description">Generar reportes y estadísticas del consultorio</p>
                    </div>
                </a>
            </div>
        </div>
    </main>
    <!-- Footer moderno -->
    <footer class="modern-footer">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        © <?php echo date('Y'); ?>
                        <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Sistema de Consultorio'; ?>.
                        Todos los derechos reservados.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex justify-content-md-end justify-content-center gap-3 mt-3 mt-md-0">
                        <a href="configuracion.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-cogs me-1"></i> Configuración
                        </a>
                        <a href="./reportes.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-chart-bar me-1"></i> Reportes
                        </a>
                        <a href="logout.php" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-sign-out-alt me-1"></i> Cerrar Sesión
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Funcionalidad moderna del dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Animación de números contadores
            function animateCounters() {
                const counters = document.querySelectorAll('.stat-number');

                counters.forEach(counter => {
                    const target = parseInt(counter.textContent);
                    const increment = target / 50;
                    let current = 0;

                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            counter.textContent = target;
                            clearInterval(timer);
                        } else {
                            counter.textContent = Math.floor(current);
                        }
                    }, 30);
                });
            }

            // Ejecutar animación después de un pequeño delay
            setTimeout(animateCounters, 500);

            // Efecto hover mejorado para las cards
            const cards = document.querySelectorAll('.stat-card, .action-card');

            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Actualización automática de estadísticas cada 5 minutos
            setInterval(function() {
                updateStats();
            }, 300000); // 5 minutos

            // Función para actualizar estadísticas
            function updateStats() {
                fetch('backend/api/dashboard_stats.php')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.querySelector('.stat-number.text-primary').textContent = data.citas_hoy;
                            document.querySelector('.stat-number.text-success').textContent = data.citas_atendidas;
                            document.querySelector('.stat-number.text-warning').textContent = data.citas_pendientes;
                            document.querySelector('.stat-number.text-info').textContent = data.total_pacientes;
                        }
                    })
                    .catch(error => console.log('Error actualizando estadísticas:', error));
            }

            // Notificaciones toast para acciones
            function showToast(message, type = 'info') {
                const toastContainer = document.getElementById('toast-container') || createToastContainer();

                const toast = document.createElement('div');
                toast.className = `toast align-items-center text-white bg-${type} border-0`;
                toast.setAttribute('role', 'alert');
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;

                toastContainer.appendChild(toast);
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();

                toast.addEventListener('hidden.bs.toast', () => {
                    toast.remove();
                });
            }

            function createToastContainer() {
                const container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(container);
                return container;
            }

            // Atajos de teclado
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            window.location.href = 'backend/citas/crear_cita.php';
                            break;
                        case '2':
                            e.preventDefault();
                            window.location.href = 'backend/pacientes/gestion_pacientes.php';
                            break;
                        case '3':
                            e.preventDefault();
                            window.location.href = 'backend/citas/gestion_citas.php';
                            break;
                    }
                }
            });

            // Mostrar información de atajos
            const shortcutsInfo = document.createElement('div');
            shortcutsInfo.className = 'position-fixed bottom-0 start-0 p-3 text-muted small';
            shortcutsInfo.innerHTML = `
                <div class="bg-white rounded p-2 shadow-sm">
                    <strong>Atajos:</strong> Ctrl+1 (Nueva Cita) | Ctrl+2 (Pacientes) | Ctrl+3 (Gestionar Citas)
                </div>
            `;
            document.body.appendChild(shortcutsInfo);

            // Ocultar atajos después de 5 segundos
            setTimeout(() => {
                shortcutsInfo.style.opacity = '0.3';
            }, 5000);

            // Mostrar/ocultar atajos al hacer hover
            shortcutsInfo.addEventListener('mouseenter', () => {
                shortcutsInfo.style.opacity = '1';
            });

            shortcutsInfo.addEventListener('mouseleave', () => {
                shortcutsInfo.style.opacity = '0.3';
            });
        });

        // Función para mostrar loading en las cards
        function showLoading(element) {
            const originalContent = element.innerHTML;
            element.innerHTML = '<div class="loading-spinner"></div>';
            return originalContent;
        }

        // Función para restaurar contenido
        function hideLoading(element, originalContent) {
            element.innerHTML = originalContent;
        }
    </script>
</body>
</html>
