<?php
/**
 * Sistema de Facturación - MarcSoftware Solutions
 * Generador de facturas para servicios de desarrollo de software
 *
 * <AUTHOR> Solutions
 * @version 2.0
 * @date 2024
 */

session_start();

// Verificar autenticación (opcional - ajustar según tu sistema)
// if (!isset($_SESSION['usuario'])) {
//     header('Location: ../../login.php');
//     exit;
// }

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../whatsapp/whatsapp_manager.php';

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Inicializar WhatsApp Manager
$whatsapp = new WhatsAppManager();

// Variables para mensajes
$success_message = '';
$error_message = '';
$warning_message = '';

// 1) Obtener datos del consultorio
try {
    $stmt = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
    $empresa = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$empresa) {
        $warning_message = "⚠️ No se encontraron datos del consultorio. Algunos campos pueden estar vacíos.";
    }
} catch (Exception $e) {
    $empresa = null;
    $error_message = "❌ Error al cargar datos del consultorio: " . $e->getMessage();
}

// 2) Procesar envío de formulario
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Validar y limpiar datos
    $concepto = trim($_POST['concepto'] ?? '');
    $precio = floatval($_POST['precio'] ?? 0);
    $moneda = $_POST['moneda'] ?? 'USD';
    $modopago = $_POST['modopago'] ?? 'Transferencia';
    $fechapago = $_POST['fechapago'] ?? date('Y-m-d');

    // Campos bancarios
    $banco_destino = $_POST['banco_destino'] ?? '';
    $cuenta_marcsoftware = trim($_POST['cuenta_marcsoftware'] ?? '');
    $beneficiario_marcsoftware = $_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions';

    // Validaciones básicas
    $errores = [];
    if (empty($concepto)) {
        $errores[] = "El concepto es obligatorio";
    }
    if ($precio <= 0) {
        $errores[] = "El precio debe ser mayor a 0";
    }
    if ($modopago === 'Transferencia' && empty($banco_destino)) {
        $errores[] = "Debe seleccionar un banco para transferencias";
    }
    if ($modopago === 'Transferencia' && empty($cuenta_marcsoftware)) {
        $errores[] = "Debe especificar la cuenta de destino";
    }
    if (!in_array($moneda, ['USD', 'DOP'])) {
        $errores[] = "Moneda no válida";
    }

    if (empty($errores)) {

        try {
            // Datos del cliente desde EMPRESA
            $cliente_nombre = $empresa['NOMBRE'] ?? 'Cliente Sin Nombre';
            $cliente_rnc = $empresa['RNC'] ?? '';
            $cliente_telefono = $empresa['TELEFONO'] ?? '';
            $cliente_email = $empresa['CORREOE'] ?? '';
            $cliente_direccion = trim(($empresa['CALLE'] ?? '') . ', ' .
                                    ($empresa['MUNICIPIO'] ?? '') . ', ' .
                                    ($empresa['PROVINCIA'] ?? ''), ', ');
            $cliente_especialidad = $empresa['ESPECIALIDAD'] ?? '';
            $cliente_subdominio = $empresa['SUBDOMINIO'] ?? '';

            // 2.1) Verificar o crear cliente
            $stmt = $pdo->prepare("SELECT CLAVE FROM CLIENTES_SOFTWARE WHERE RNC = ? OR NOMBRE_COMPLETO = ?");
            $stmt->execute([$cliente_rnc, $cliente_nombre]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$row) {
                // Crear tabla clientes si no existe
                $pdo->exec("CREATE TABLE IF NOT EXISTS CLIENTES_SOFTWARE (
                    CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                    NOMBRE_COMPLETO VARCHAR(200) NOT NULL,
                    RNC VARCHAR(20),
                    TELEFONO VARCHAR(20),
                    EMAIL VARCHAR(100),
                    DIRECCION VARCHAR(200),
                    CIUDAD VARCHAR(100),
                    PROVINCIA VARCHAR(100),
                    ESPECIALIDAD VARCHAR(100),
                    SUBDOMINIO VARCHAR(50),
                    FECHA_REGISTRO DATE DEFAULT (CURDATE()),
                    ESTATUS CHAR(1) DEFAULT 'A',
                    INDEX idx_rnc (RNC),
                    INDEX idx_nombre (NOMBRE_COMPLETO)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

                $ins = $pdo->prepare("INSERT INTO CLIENTES_SOFTWARE
                    (NOMBRE_COMPLETO,RNC,TELEFONO,EMAIL,DIRECCION,CIUDAD,PROVINCIA,ESPECIALIDAD,SUBDOMINIO,FECHA_REGISTRO)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())");
                $ins->execute([
                    $cliente_nombre,
                    $cliente_rnc,
                    $cliente_telefono,
                    $cliente_email,
                    $empresa['CALLE'] ?? '',
                    $empresa['MUNICIPIO'] ?? '',
                    $empresa['PROVINCIA'] ?? '',
                    $cliente_especialidad,
                    $cliente_subdominio
                ]);
                $cliente_clave = $pdo->lastInsertId();
            } else {
                $cliente_clave = $row['CLAVE'];
            }

            // 2.2) Crear tabla facturas si no existe
            $pdo->exec("CREATE TABLE IF NOT EXISTS FACTURAS_SOFTWARE (
                CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                CLIENTE_CLAVE INT NOT NULL,
                NUMERO_FACTURA VARCHAR(20) UNIQUE NOT NULL,
                CONCEPTO TEXT NOT NULL,
                PRECIO DECIMAL(10,2) NOT NULL,
                MONEDA VARCHAR(5) DEFAULT 'USD',
                MODO_PAGO VARCHAR(20) DEFAULT 'Transferencia',
                BANCO_DESTINO VARCHAR(50),
                CUENTA_DESTINO VARCHAR(30),
                BENEFICIARIO VARCHAR(100),
                FECHA_FACTURA DATE NOT NULL,
                FECHA_VENCIMIENTO DATE,
                ESTATUS VARCHAR(15) DEFAULT 'PENDIENTE',
                FECHA_CREACION DATETIME DEFAULT CURRENT_TIMESTAMP,
                FECHA_ACTUALIZACION DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                USUARIO_CREACION INT,
                NOTAS TEXT,
                FOREIGN KEY (CLIENTE_CLAVE) REFERENCES CLIENTES_SOFTWARE(CLAVE),
                INDEX idx_numero (NUMERO_FACTURA),
                INDEX idx_cliente (CLIENTE_CLAVE),
                INDEX idx_fecha (FECHA_FACTURA),
                INDEX idx_estatus (ESTATUS)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // 2.3) Generar número automático
            $stmt_max = $pdo->query("SELECT MAX(CAST(SUBSTRING(NUMERO_FACTURA,4) AS UNSIGNED)) AS max_num
                                   FROM FACTURAS_SOFTWARE
                                   WHERE NUMERO_FACTURA LIKE 'MS-%'");
            $max_result = $stmt_max->fetch(PDO::FETCH_ASSOC);
            $max_num = $max_result['max_num'] ?? 1000;
            $numero_factura = 'MS-' . str_pad($max_num + 1, 4, '0', STR_PAD_LEFT);

            // 2.4) Incluir datos de transferencia en el concepto si aplica
            $concepto_completo = $concepto;
            if ($modopago === 'Transferencia' && $banco_destino) {
                $concepto_completo .= "\n\n" . str_repeat("=", 40) . "\n";
                $concepto_completo .= "DATOS PARA TRANSFERENCIA:\n";
                $concepto_completo .= str_repeat("=", 40) . "\n";
                $concepto_completo .= "🏦 Banco: {$banco_destino}\n";
                $concepto_completo .= "💳 Cuenta: {$cuenta_marcsoftware}\n";
                $concepto_completo .= "👤 Beneficiario: {$beneficiario_marcsoftware}\n";
                $concepto_completo .= str_repeat("=", 40);
            }

            // 2.5) Calcular fecha de vencimiento
            $fecha_vencimiento = date('Y-m-d', strtotime('+30 days', strtotime($fechapago)));

            // 2.6) Insertar la factura
            $stmt_insert = $pdo->prepare("INSERT INTO FACTURAS_SOFTWARE
                (CLIENTE_CLAVE, NUMERO_FACTURA, CONCEPTO, PRECIO, MONEDA, MODO_PAGO,
                 BANCO_DESTINO, CUENTA_DESTINO, BENEFICIARIO, FECHA_FACTURA, FECHA_VENCIMIENTO,
                 FECHA_CREACION, USUARIO_CREACION)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)");

            $usuario_id = $_SESSION['usuario_id'] ?? 1; // Ajustar según tu sistema de usuarios

            $stmt_insert->execute([
                $cliente_clave,
                $numero_factura,
                $concepto_completo,
                $precio,
                $moneda,
                $modopago,
                $banco_destino,
                $cuenta_marcsoftware,
                $beneficiario_marcsoftware,
                $fechapago,
                $fecha_vencimiento,
                $usuario_id
            ]);

            // 3) Mensaje de éxito con integración de WhatsApp
            $factura_id = $pdo->lastInsertId();

            // Preparar mensaje de WhatsApp
            $whatsapp_message = urlencode(
                "🧾 *NUEVA FACTURA GENERADA*\n\n" .
                "📄 *Factura:* {$numero_factura}\n" .
                "👤 *Cliente:* {$cliente_nombre}\n" .
                "💰 *Monto:* {$moneda} " . number_format($precio, 2) . "\n" .
                "📅 *Vence:* " . date('d/m/Y', strtotime($fecha_vencimiento)) . "\n" .
                "💳 *Pago:* {$modopago}\n\n" .
                ($modopago === 'Transferencia' && $banco_destino ?
                    "🏦 *Datos Bancarios:*\n" .
                    "• Banco: {$banco_destino}\n" .
                    "• Cuenta: {$cuenta_marcsoftware}\n" .
                    "• Beneficiario: {$beneficiario_marcsoftware}\n\n" : ""
                ) .
                "Gracias por elegir MarcSoftware Solutions 🚀\n" .
                "Para descargar su factura: [ENLACE_FACTURA]"
            );

            // Obtener teléfono del cliente (si existe)
            $cliente_telefono_clean = '';
            if (!empty($cliente_telefono)) {
                // Limpiar y formatear número de teléfono
                $cliente_telefono_clean = preg_replace('/[^0-9]/', '', $cliente_telefono);
                // Agregar código de país si no lo tiene (asumiendo República Dominicana +1809)
                if (strlen($cliente_telefono_clean) === 10) {
                    $cliente_telefono_clean = '1809' . $cliente_telefono_clean;
                } elseif (strlen($cliente_telefono_clean) === 7) {
                    $cliente_telefono_clean = '1809' . $cliente_telefono_clean;
                }
            }

            $success_message = "
                <div class='alert alert-success alert-dismissible fade show' role='alert'>
                    <h4 class='alert-heading'>✅ ¡Factura Creada Exitosamente!</h4>
                    <hr>
                    <p class='mb-1'><strong>📄 Número:</strong> {$numero_factura}</p>
                    <p class='mb-1'><strong>👤 Cliente:</strong> {$cliente_nombre}</p>
                    <p class='mb-1'><strong>💰 Monto:</strong> {$moneda} " . number_format($precio, 2) . "</p>
                    <p class='mb-1'><strong>📅 Vence:</strong> " . date('d/m/Y', strtotime($fecha_vencimiento)) . "</p>
                    <p class='mb-1'><strong>💳 Pago:</strong> {$modopago}</p>
                    <hr>
                    <div class='d-grid gap-2 d-md-flex justify-content-md-start mb-3'>
                        <a href='../api/generar_factura_pdf.php?id_factura={$factura_id}&subdominio=" . ($empresa['SUBDOMINIO'] ?? 'default') . "' target='_blank' class='btn btn-primary'>
                            🖨️ Imprimir Factura
                        </a>
                        <a href='ver_factura.php?id={$factura_id}' class='btn btn-info'>
                            👁️ Ver Detalles
                        </a>
                        <a href='lista_facturas.php' class='btn btn-secondary'>
                            📋 Ver Todas las Facturas
                        </a>
                    </div>";

            // Botones de WhatsApp
            if (!empty($cliente_telefono_clean)) {
                $success_message .= "
                    <div class='alert alert-info'>
                        <h6><i class='bi bi-whatsapp'></i> Enviar por WhatsApp</h6>
                        <div class='d-grid gap-2 d-md-flex'>
                            <a href='https://wa.me/{$cliente_telefono_clean}?text={$whatsapp_message}'
                               target='_blank' class='btn btn-success'>
                                <i class='bi bi-whatsapp'></i> Enviar al Cliente
                            </a>
                            <button type='button' class='btn btn-outline-success' onclick='copyWhatsAppMessage()'>
                                <i class='bi bi-clipboard'></i> Copiar Mensaje
                            </button>
                        </div>
                        <small class='text-muted'>Teléfono detectado: +{$cliente_telefono_clean}</small>
                    </div>";
            } else {
                $success_message .= "
                    <div class='alert alert-warning'>
                        <h6><i class='bi bi-whatsapp'></i> WhatsApp</h6>
                        <p class='mb-2'>No se detectó teléfono del cliente. Puede enviar manualmente:</p>
                        <div class='d-grid gap-2 d-md-flex'>
                            <button type='button' class='btn btn-success' onclick='openWhatsAppManual()'>
                                <i class='bi bi-whatsapp'></i> Abrir WhatsApp Web
                            </button>
                            <button type='button' class='btn btn-outline-success' onclick='copyWhatsAppMessage()'>
                                <i class='bi bi-clipboard'></i> Copiar Mensaje
                            </button>
                        </div>
                    </div>";
            }

            $success_message .= "
                    <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                </div>

                <script>
                    const whatsappMessage = `{$whatsapp_message}`;

                    function copyWhatsAppMessage() {
                        const decodedMessage = decodeURIComponent(whatsappMessage);
                        navigator.clipboard.writeText(decodedMessage).then(function() {
                            alert('✅ Mensaje copiado al portapapeles');
                        });
                    }

                    function openWhatsAppManual() {
                        window.open('https://web.whatsapp.com/', '_blank');
                        copyWhatsAppMessage();
                    }
                </script>";

            // Limpiar formulario después del éxito
            $_POST = [];

        } catch (Exception $e) {
            $error_message = "❌ Error al crear la factura: " . htmlspecialchars($e->getMessage());
        }
    } else {
        $error_message = "❌ Errores en el formulario:<br>• " . implode("<br>• ", $errores);
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Factura - MarcSoftware Solutions</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #0d6efd;
            --success-color: #198754;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #0dcaf0;
            --light-bg: #f8f9fa;
            --border-radius: 0.75rem;
            --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            line-height: 1.6;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            padding: 1rem 1.5rem;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            transition: all 0.15s ease-in-out;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .btn {
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.15s ease-in-out;
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1.5rem;
        }

        .page-title {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .empresa-info {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        }

        .bank-fields {
            background: linear-gradient(135deg, #fff3e0, #f1f8e9);
        }

        .required {
            color: var(--danger-color);
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0.5rem;
            }

            .card-body {
                padding: 1rem;
            }
        }
    </style>

    <script>
        function toggleBankFields() {
            const modo = document.getElementById('modopago').value;
            const bankFields = document.getElementById('bank-fields');

            if (modo === 'Transferencia') {
                bankFields.style.display = 'block';
                bankFields.classList.add('fade-in');
            } else {
                bankFields.style.display = 'none';
                bankFields.classList.remove('fade-in');
            }
        }

        function validateForm() {
            const concepto = document.querySelector('textarea[name="concepto"]').value.trim();
            const precio = parseFloat(document.querySelector('input[name="precio"]').value);
            const modopago = document.getElementById('modopago').value;
            const banco = document.querySelector('select[name="banco_destino"]').value;
            const cuenta = document.querySelector('input[name="cuenta_marcsoftware"]').value.trim();

            let errors = [];

            if (!concepto) {
                errors.push('El concepto es obligatorio');
            }

            if (!precio || precio <= 0) {
                errors.push('El precio debe ser mayor a 0');
            }

            if (modopago === 'Transferencia') {
                if (!banco) {
                    errors.push('Debe seleccionar un banco para transferencias');
                }
                if (!cuenta) {
                    errors.push('Debe especificar la cuenta de destino');
                }
            }

            if (errors.length > 0) {
                alert('Errores en el formulario:\\n• ' + errors.join('\\n• '));
                return false;
            }

            return true;
        }

        document.addEventListener('DOMContentLoaded', function() {
            toggleBankFields();

            // Auto-format price input
            const priceInput = document.querySelector('input[name="precio"]');
            if (priceInput) {
                priceInput.addEventListener('blur', function() {
                    const value = parseFloat(this.value);
                    if (!isNaN(value)) {
                        this.value = value.toFixed(2);
                    }
                });
            }
        });
    </script>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="page-title">
                <i class="bi bi-receipt-cutoff"></i>
                Crear Factura - MarcSoftware Solutions
            </h1>
            <p class="page-subtitle">
                <i class="bi bi-gear-fill"></i>
                Sistema de facturación para servicios de desarrollo de software
            </p>
        </div>

        <!-- Mensajes de estado -->
        <?php if ($success_message): ?>
            <?= $success_message ?>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <?= $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($warning_message): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i>
                <?= $warning_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Datos del Consultorio -->
        <?php if ($empresa): ?>
        <div class="card mb-4">
            <div class="card-header empresa-info">
                <h5>
                    <i class="bi bi-building"></i>
                    Datos del Consultorio
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong><i class="bi bi-person-badge"></i> Nombre:</strong>
                            <?= htmlspecialchars($empresa['NOMBRE'] ?? 'No especificado') ?>
                        </p>
                        <p class="mb-2">
                            <strong><i class="bi bi-card-text"></i> RNC:</strong>
                            <?= htmlspecialchars($empresa['RNC'] ?? 'No especificado') ?>
                        </p>
                        <p class="mb-2">
                            <strong><i class="bi bi-heart-pulse"></i> Especialidad:</strong>
                            <?= htmlspecialchars($empresa['ESPECIALIDAD'] ?? 'No especificado') ?>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong><i class="bi bi-globe"></i> Subdominio:</strong>
                            <?= htmlspecialchars($empresa['SUBDOMINIO'] ?? 'No especificado') ?>
                        </p>
                        <p class="mb-2">
                            <strong><i class="bi bi-geo-alt"></i> Dirección:</strong>
                            <?= htmlspecialchars(trim(($empresa['CALLE'] ?? '') . ', ' .
                                                     ($empresa['MUNICIPIO'] ?? '') . ', ' .
                                                     ($empresa['PROVINCIA'] ?? ''), ', ') ?: 'No especificada') ?>
                        </p>
                        <p class="mb-2">
                            <strong><i class="bi bi-telephone"></i> Contacto:</strong>
                            <?= htmlspecialchars($empresa['TELEFONO'] ?? 'No especificado') ?>
                            <?php if (!empty($empresa['CORREOE'])): ?>
                                · <strong><i class="bi bi-envelope"></i></strong> <?= htmlspecialchars($empresa['CORREOE']) ?>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Formulario de Facturación -->
        <form method="POST" onsubmit="return validateForm()">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>
                        <i class="bi bi-currency-dollar"></i>
                        Detalles de Facturación
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="bi bi-file-text"></i>
                            Concepto/Servicio <span class="required">*</span>
                        </label>
                        <textarea name="concepto" class="form-control" rows="4" required
                                  placeholder="Describe detalladamente el servicio o producto facturado..."><?= htmlspecialchars($_POST['concepto'] ?? '') ?></textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            Incluye una descripción clara del servicio prestado
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="bi bi-cash-stack"></i>
                                Precio <span class="required">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-currency-exchange"></i>
                                </span>
                                <input type="number" name="precio" step="0.01" min="0.01" class="form-control"
                                       value="<?= htmlspecialchars($_POST['precio'] ?? '') ?>" required
                                       placeholder="0.00">
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="bi bi-currency-dollar"></i>
                                Moneda
                            </label>
                            <select name="moneda" class="form-select">
                                <option value="USD" <?= ($_POST['moneda'] ?? 'USD') === 'USD' ? 'selected' : '' ?>>
                                    💵 USD - Dólar Americano
                                </option>
                                <option value="DOP" <?= ($_POST['moneda'] ?? '') === 'DOP' ? 'selected' : '' ?>>
                                    💰 DOP - Peso Dominicano
                                </option>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="bi bi-credit-card"></i>
                                Modo de Pago
                            </label>
                            <select id="modopago" name="modopago" class="form-select" onchange="toggleBankFields()">
                                <option value="Transferencia" <?= ($_POST['modopago'] ?? 'Transferencia') === 'Transferencia' ? 'selected' : '' ?>>
                                    🏦 Transferencia Bancaria
                                </option>
                                <option value="Efectivo" <?= ($_POST['modopago'] ?? '') === 'Efectivo' ? 'selected' : '' ?>>
                                    💵 Efectivo
                                </option>
                                <option value="Cheque" <?= ($_POST['modopago'] ?? '') === 'Cheque' ? 'selected' : '' ?>>
                                    📝 Cheque
                                </option>
                                <option value="Tarjeta" <?= ($_POST['modopago'] ?? '') === 'Tarjeta' ? 'selected' : '' ?>>
                                    💳 Tarjeta de Crédito/Débito
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Datos Bancarios (solo para transferencias) -->
            <div id="bank-fields" class="card mb-4 bank-fields" style="display:none;">
                <div class="card-header">
                    <h5>
                        <i class="bi bi-bank"></i>
                        Datos Bancarios para Transferencia
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Importante:</strong> Estos datos aparecerán en la factura para que el cliente pueda realizar la transferencia.
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="bi bi-building"></i>
                                Banco Destino <span class="required">*</span>
                            </label>
                            <select name="banco_destino" class="form-select">
                                <option value="">Seleccionar banco...</option>
                                <option value="Banco Popular" <?= ($_POST['banco_destino'] ?? '') === 'Banco Popular' ? 'selected' : '' ?>>
                                    🏛️ Banco Popular Dominicano
                                </option>
                                <option value="Banreservas" <?= ($_POST['banco_destino'] ?? '') === 'Banreservas' ? 'selected' : '' ?>>
                                    🏛️ Banco de Reservas
                                </option>
                                <option value="Banco BHD León" <?= ($_POST['banco_destino'] ?? '') === 'Banco BHD León' ? 'selected' : '' ?>>
                                    🏛️ Banco BHD León
                                </option>
                                <option value="Scotiabank" <?= ($_POST['banco_destino'] ?? '') === 'Scotiabank' ? 'selected' : '' ?>>
                                    🏛️ Scotiabank
                                </option>
                                <option value="Banco Promerica" <?= ($_POST['banco_destino'] ?? '') === 'Banco Promerica' ? 'selected' : '' ?>>
                                    🏛️ Banco Promerica
                                </option>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="bi bi-credit-card-2-front"></i>
                                Cuenta MarcSoftware <span class="required">*</span>
                            </label>
                            <input type="text" name="cuenta_marcsoftware" class="form-control"
                                   value="<?= htmlspecialchars($_POST['cuenta_marcsoftware'] ?? '') ?>"
                                   placeholder="Ej: 123-456789-0">
                            <div class="form-text">
                                <i class="bi bi-shield-check"></i>
                                Número de cuenta donde se recibirá el pago
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="bi bi-person-badge"></i>
                                Beneficiario
                            </label>
                            <input type="text" name="beneficiario_marcsoftware" class="form-control"
                                   value="<?= htmlspecialchars($_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions') ?>"
                                   placeholder="Nombre del beneficiario">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fecha y Botón de Envío -->
            <div class="row mb-4">
                <div class="col-md-6 mb-3">
                    <label class="form-label">
                        <i class="bi bi-calendar-event"></i>
                        Fecha de Factura
                    </label>
                    <input type="date" name="fechapago" class="form-control"
                           value="<?= htmlspecialchars($_POST['fechapago'] ?? date('Y-m-d')) ?>"
                           max="<?= date('Y-m-d') ?>">
                    <div class="form-text">
                        <i class="bi bi-clock"></i>
                        La fecha de vencimiento será 30 días después
                    </div>
                </div>
            </div>

            <!-- Botones de Acción -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="reset" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                    Limpiar Formulario
                </button>
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="bi bi-receipt-cutoff"></i>
                    Generar Factura
                </button>
            </div>
        </form>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <?php
    // Incluir botón flotante de WhatsApp
    require_once __DIR__ . '/../whatsapp/whatsapp_widget.php';
    incluir_whatsapp_flotante(
        null, // Usar número por defecto
        "Hola, necesito ayuda con el sistema de facturación de MarcSoftware Solutions"
    );
    ?>
</body>
</html>
