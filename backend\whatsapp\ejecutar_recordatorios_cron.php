<?php
/**
 * Script Cron para Recordatorios Automáticos de Citas
 * Este script debe ejecutarse mediante cron job para automatizar los recordatorios
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

// Configuración para ejecución por línea de comandos
if (php_sapi_name() !== 'cli') {
    die("Este script solo puede ejecutarse desde línea de comandos\n");
}

// Configurar zona horaria
date_default_timezone_set('America/Santo_Domingo');

// Incluir archivos necesarios
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/whatsapp_manager.php';
require_once __DIR__ . '/config_whatsapp.php';

// Configurar logging
$log_file = __DIR__ . '/logs/recordatorios_cron.log';
$log_dir = dirname($log_file);

if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

function escribir_log($mensaje) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$mensaje}\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry; // También mostrar en consola
}

escribir_log("=== INICIO EJECUCIÓN RECORDATORIOS AUTOMÁTICOS ===");

try {
    $whatsapp = new WhatsAppManager();
    
    // Obtener configuración
    $stmt = $pdo->query("SELECT * FROM CONFIG_RECORDATORIOS_CITAS ORDER BY ID DESC LIMIT 1");
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$config) {
        escribir_log("ERROR: No se encontró configuración de recordatorios");
        exit(1);
    }
    
    if (!$config['ACTIVO']) {
        escribir_log("INFO: Recordatorios automáticos desactivados");
        exit(0);
    }
    
    $config['DIAS_ANTICIPACION'] = json_decode($config['DIAS_ANTICIPACION'], true);
    escribir_log("INFO: Configuración cargada - Días: " . implode(',', $config['DIAS_ANTICIPACION']));
    
    // Verificar horario de ejecución
    $hora_actual = date('H:i:s');
    $en_horario = ($hora_actual >= $config['HORARIO_INICIO'] && $hora_actual <= $config['HORARIO_FIN']);
    
    if (!$en_horario) {
        escribir_log("INFO: Fuera del horario de envío ({$config['HORARIO_INICIO']} - {$config['HORARIO_FIN']})");
        exit(0);
    }
    
    // Verificar si es fin de semana
    $es_fin_semana = (date('N') >= 6); // 6 = Sábado, 7 = Domingo
    if ($es_fin_semana && !$config['ENVIAR_FINES_SEMANA']) {
        escribir_log("INFO: Es fin de semana y el envío está desactivado");
        exit(0);
    }
    
    // Verificar límite diario
    $stmt = $pdo->query("
        SELECT COUNT(*) as enviados_hoy 
        FROM RECORDATORIOS_CITAS_WHATSAPP 
        WHERE DATE(FECHA_ENVIO) = CURDATE() AND TIPO_RECORDATORIO = 'AUTOMATICO'
    ");
    $enviados_hoy = $stmt->fetch()['enviados_hoy'];
    
    if ($enviados_hoy >= $config['MAX_RECORDATORIOS_DIA']) {
        escribir_log("INFO: Límite diario alcanzado ({$enviados_hoy}/{$config['MAX_RECORDATORIOS_DIA']})");
        exit(0);
    }
    
    $total_enviados = 0;
    $total_errores = 0;
    
    // Procesar cada día de anticipación
    foreach ($config['DIAS_ANTICIPACION'] as $dias) {
        $fecha_objetivo = date('Y-m-d', strtotime("+{$dias} days"));
        escribir_log("INFO: Procesando citas para {$fecha_objetivo} ({$dias} días de anticipación)");
        
        // Obtener citas para la fecha objetivo
        $stmt = $pdo->prepare("
            SELECT 
                c.*,
                p.NOMBREAPELLIDO as paciente_nombre,
                p.TELEFONO as paciente_telefono,
                p.CELULAR as paciente_celular,
                p.CEDULA as paciente_cedula
            FROM CITAMEDIC c
            LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE OR c.NSS = p.CEDULA
            WHERE c.FECHACON = ?
            AND c.ESTATUS IN (1, 3, 6)
            AND (p.TELEFONO IS NOT NULL OR p.CELULAR IS NOT NULL)
        ");
        
        $stmt->execute([$fecha_objetivo]);
        $citas = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        escribir_log("INFO: Encontradas " . count($citas) . " citas para {$fecha_objetivo}");
        
        foreach ($citas as $cita) {
            // Verificar límite diario actualizado
            if ($total_enviados >= ($config['MAX_RECORDATORIOS_DIA'] - $enviados_hoy)) {
                escribir_log("INFO: Límite diario alcanzado durante ejecución");
                break 2;
            }
            
            // Verificar si ya se envió recordatorio
            $stmt_check = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM RECORDATORIOS_CITAS_WHATSAPP 
                WHERE CITA_ID = ? AND DIAS_ANTICIPACION = ? AND DATE(FECHA_ENVIO) = CURDATE()
            ");
            $stmt_check->execute([$cita['CLAVE'], $dias]);
            
            if ($stmt_check->fetch()['count'] > 0) {
                escribir_log("INFO: Recordatorio ya enviado para cita {$cita['CLAVE']}");
                continue;
            }
            
            $telefono = !empty($cita['paciente_celular']) ? $cita['paciente_celular'] : $cita['paciente_telefono'];
            
            if (empty($telefono)) {
                escribir_log("WARNING: Cita {$cita['CLAVE']} sin teléfono");
                continue;
            }
            
            try {
                // Generar mensaje
                $cita['dias_hasta_cita'] = $dias;
                $mensaje = generarMensajeRecordatorioCita($cita);
                
                // Registrar en base de datos
                $stmt_log = $pdo->prepare("
                    INSERT INTO RECORDATORIOS_CITAS_WHATSAPP 
                    (CITA_ID, PACIENTE_TELEFONO, MENSAJE, USUARIO_ENVIO, TIPO_RECORDATORIO, DIAS_ANTICIPACION)
                    VALUES (?, ?, ?, ?, 'AUTOMATICO', ?)
                ");
                
                $stmt_log->execute([
                    $cita['CLAVE'],
                    $telefono,
                    $mensaje,
                    'Sistema-Cron',
                    $dias
                ]);
                
                $total_enviados++;
                escribir_log("SUCCESS: Recordatorio preparado para {$cita['paciente_nombre']} ({$telefono})");
                
                // Pausa pequeña para evitar sobrecarga
                usleep(100000); // 0.1 segundos
                
            } catch (Exception $e) {
                $total_errores++;
                escribir_log("ERROR: Fallo al procesar cita {$cita['CLAVE']}: " . $e->getMessage());
            }
        }
    }
    
    // Registrar estadísticas de ejecución
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS LOGS_EJECUCION_CRON (
                ID INT AUTO_INCREMENT PRIMARY KEY,
                FECHA_EJECUCION DATETIME DEFAULT CURRENT_TIMESTAMP,
                RECORDATORIOS_ENVIADOS INT DEFAULT 0,
                ERRORES INT DEFAULT 0,
                TIEMPO_EJECUCION DECIMAL(5,2),
                NOTAS TEXT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $tiempo_fin = microtime(true);
        $tiempo_inicio = $tiempo_inicio ?? $tiempo_fin;
        $tiempo_ejecucion = round($tiempo_fin - $tiempo_inicio, 2);
        
        $stmt = $pdo->prepare("
            INSERT INTO LOGS_EJECUCION_CRON 
            (RECORDATORIOS_ENVIADOS, ERRORES, TIEMPO_EJECUCION, NOTAS)
            VALUES (?, ?, ?, ?)
        ");
        
        $notas = "Días procesados: " . implode(',', $config['DIAS_ANTICIPACION']);
        $stmt->execute([$total_enviados, $total_errores, $tiempo_ejecucion, $notas]);
        
    } catch (Exception $e) {
        escribir_log("ERROR: No se pudo registrar estadísticas: " . $e->getMessage());
    }
    
    escribir_log("RESUMEN: {$total_enviados} recordatorios enviados, {$total_errores} errores");
    escribir_log("=== FIN EJECUCIÓN RECORDATORIOS AUTOMÁTICOS ===");
    
    // Código de salida
    exit($total_errores > 0 ? 1 : 0);
    
} catch (Exception $e) {
    escribir_log("ERROR CRÍTICO: " . $e->getMessage());
    escribir_log("=== FIN EJECUCIÓN CON ERROR ===");
    exit(1);
}

// Función para generar mensaje de recordatorio
function generarMensajeRecordatorioCita($cita) {
    $dias = $cita['dias_hasta_cita'];
    $fecha_cita = date('d/m/Y', strtotime($cita['FECHACON']));
    $hora_cita = date('h:i A', strtotime($cita['HORACON']));
    
    if ($dias == 0) {
        $cuando = "HOY";
        $emoji = "🚨";
    } elseif ($dias == 1) {
        $cuando = "MAÑANA";
        $emoji = "⏰";
    } else {
        $cuando = "en {$dias} días";
        $emoji = "📅";
    }
    
    $mensaje = "{$emoji} *RECORDATORIO DE CITA MÉDICA*\n\n";
    $mensaje .= "Estimado/a *{$cita['paciente_nombre']}*,\n\n";
    $mensaje .= "Le recordamos que tiene una cita médica programada para *{$cuando}*:\n\n";
    $mensaje .= "📅 *Fecha:* {$fecha_cita}\n";
    $mensaje .= "🕐 *Hora:* {$hora_cita}\n";
    
    if (!empty($cita['CONSULTORIO'])) {
        $mensaje .= "🏥 *Consultorio:* {$cita['CONSULTORIO']}\n";
    }
    
    if (!empty($cita['OBSERVACION'])) {
        $mensaje .= "📝 *Observaciones:* {$cita['OBSERVACION']}\n";
    }
    
    $mensaje .= "\n📋 *Recomendaciones:*\n";
    $mensaje .= "• Llegue 15 minutos antes de su cita\n";
    $mensaje .= "• Traiga su cédula de identidad\n";
    $mensaje .= "• Traiga su tarjeta de seguro (si aplica)\n";
    $mensaje .= "• Si tiene estudios previos, tráigalos\n\n";
    
    if ($dias <= 1) {
        $mensaje .= "⚠️ *IMPORTANTE:* Si no puede asistir, por favor comuníquese con nosotros lo antes posible.\n\n";
    } else {
        $mensaje .= "Si necesita reprogramar su cita, contáctenos con anticipación.\n\n";
    }
    
    $mensaje .= "📞 *Contacto:* " . WHATSAPP_SOPORTE . "\n";
    $mensaje .= "🏥 *Consultorio Médico*\n";
    $mensaje .= "Cuidando su salud con profesionalismo ❤️";
    
    return $mensaje;
}
?>
