<?php
/**
 * Sistema de Gestión de WhatsApp para Consultorio Médico
 * Envío de mensajes de citas, recordatorios y notificaciones
 * 
 * <AUTHOR> de Consultorio
 * @version 1.0
 * @date 2024
 */

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], ['admin', 'doctor', 'secretaria'])) {
    header('Location: ../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';

// Configuración de WhatsApp API (ajustar según tu proveedor)
class WhatsAppManager {
    private $pdo;
    private $api_url;
    private $api_token;
    private $phone_number_id;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        
        // Configuración de la API de WhatsApp Business
        // Puedes usar servicios como: Twilio, Meta WhatsApp Business API, o WhatsApp Business API Cloud
        $this->api_url = "https://graph.facebook.com/v18.0/"; // Meta WhatsApp Business API
        $this->api_token = "TU_TOKEN_DE_ACCESO"; // Reemplazar con tu token real
        $this->phone_number_id = "TU_PHONE_NUMBER_ID"; // Reemplazar con tu Phone Number ID
    }
    
    /**
     * Formatear número de teléfono para WhatsApp
     */
    private function formatPhoneNumber($phone) {
        // Remover caracteres no numéricos
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Si el número empieza con 1 (República Dominicana), agregar código de país
        if (strlen($phone) == 10 && substr($phone, 0, 1) == '8') {
            $phone = '1' . $phone; // +1 para República Dominicana
        }
        
        return $phone;
    }
    
    /**
     * Enviar mensaje de WhatsApp
     */
    public function sendMessage($to, $message) {
        $to = $this->formatPhoneNumber($to);
        
        $data = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $message
            ]
        ];
        
        $headers = [
            'Authorization: Bearer ' . $this->api_token,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->api_url . $this->phone_number_id . '/messages');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'success' => $httpCode == 200,
            'response' => json_decode($response, true),
            'http_code' => $httpCode
        ];
    }
    
    /**
     * Obtener citas del día
     */
    public function getCitasHoy() {
        $hoy = date('Y-m-d');
        $stmt = $this->pdo->prepare("
            SELECT c.*, p.NOMBRES, p.APELLIDOS, p.TELEFONO, p.CELULAR, p.ECORREO
            FROM CITAMEDIC c
            LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
            WHERE c.FECHACON = ? AND c.ESTATUS IN (3, 6)
            ORDER BY c.HORACON
        ");
        $stmt->execute([$hoy]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Obtener citas de mañana
     */
    public function getCitasManana() {
        $manana = date('Y-m-d', strtotime('+1 day'));
        $stmt = $this->pdo->prepare("
            SELECT c.*, p.NOMBRES, p.APELLIDOS, p.TELEFONO, p.CELULAR, p.ECORREO
            FROM CITAMEDIC c
            LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
            WHERE c.FECHACON = ? AND c.ESTATUS IN (3, 6)
            ORDER BY c.HORACON
        ");
        $stmt->execute([$manana]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Obtener pacientes con análisis pendientes
     */
    public function getPacientesAnalisisPendientes() {
        $stmt = $this->pdo->prepare("
            SELECT DISTINCT p.NOMBRES, p.APELLIDOS, p.TELEFONO, p.CELULAR, p.ECORREO,
                   c.FECHACON, c.HORACON
            FROM PACIENTES p
            JOIN CITAMEDIC c ON p.CLAVE = c.CLAVEPAC
            WHERE c.FECHACON BETWEEN DATE_SUB(NOW(), INTERVAL 30 DAY) AND NOW()
            AND c.ESTATUS = 3
            AND p.TELEFONO IS NOT NULL AND p.TELEFONO != ''
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Enviar confirmación de cita
     */
    public function enviarConfirmacionCita($cita) {
        $telefono = $cita['CELULAR'] ?: $cita['TELEFONO'];
        if (!$telefono) return false;
        
        $fecha = date('d/m/Y', strtotime($cita['FECHACON']));
        $hora = date('g:i A', strtotime($cita['HORACON']));
        $nombre = trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS']);
        
        $mensaje = "🏥 *CONFIRMACIÓN DE CITA MÉDICA*\n\n";
        $mensaje .= "Estimado/a *{$nombre}*,\n\n";
        $mensaje .= "Su cita médica ha sido confirmada:\n";
        $mensaje .= "📅 *Fecha:* {$fecha}\n";
        $mensaje .= "🕐 *Hora:* {$hora}\n";
        $mensaje .= "🏥 *Consultorio:* " . ($cita['CONSULTORIO'] ?? 'Principal') . "\n\n";
        $mensaje .= "📋 *Recomendaciones:*\n";
        $mensaje .= "• Llegar 15 minutos antes\n";
        $mensaje .= "• Traer documento de identidad\n";
        $mensaje .= "• Traer carnet del seguro (si aplica)\n\n";
        $mensaje .= "Para reprogramar o cancelar, contacte al consultorio.\n\n";
        $mensaje .= "¡Esperamos verle pronto! 👩‍⚕️👨‍⚕️";
        
        return $this->sendMessage($telefono, $mensaje);
    }
    
    /**
     * Enviar recordatorio de cita
     */
    public function enviarRecordatorioCita($cita) {
        $telefono = $cita['CELULAR'] ?: $cita['TELEFONO'];
        if (!$telefono) return false;
        
        $fecha = date('d/m/Y', strtotime($cita['FECHACON']));
        $hora = date('g:i A', strtotime($cita['HORACON']));
        $nombre = trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS']);
        
        $mensaje = "⏰ *RECORDATORIO DE CITA MÉDICA*\n\n";
        $mensaje .= "Estimado/a *{$nombre}*,\n\n";
        $mensaje .= "Le recordamos su cita médica para mañana:\n";
        $mensaje .= "📅 *Fecha:* {$fecha}\n";
        $mensaje .= "🕐 *Hora:* {$hora}\n";
        $mensaje .= "🏥 *Consultorio:* " . ($cita['CONSULTORIO'] ?? 'Principal') . "\n\n";
        $mensaje .= "📝 *No olvide traer:*\n";
        $mensaje .= "• Documento de identidad\n";
        $mensaje .= "• Carnet del seguro\n";
        $mensaje .= "• Estudios médicos previos\n\n";
        $mensaje .= "Si no puede asistir, por favor contacte al consultorio.\n\n";
        $mensaje .= "¡Nos vemos mañana! 🩺";
        
        return $this->sendMessage($telefono, $mensaje);
    }
    
    /**
     * Enviar recordatorio de análisis
     */
    public function enviarRecordatorioAnalisis($paciente) {
        $telefono = $paciente['CELULAR'] ?: $paciente['TELEFONO'];
        if (!$telefono) return false;
        
        $nombre = trim($paciente['NOMBRES'] . ' ' . $paciente['APELLIDOS']);
        
        $mensaje = "🔬 *RECORDATORIO DE ANÁLISIS MÉDICOS*\n\n";
        $mensaje .= "Estimado/a *{$nombre}*,\n\n";
        $mensaje .= "Le recordamos que tiene análisis médicos pendientes de su última consulta.\n\n";
        $mensaje .= "📋 *Recomendaciones:*\n";
        $mensaje .= "• Realizar los estudios lo antes posible\n";
        $mensaje .= "• Seguir las indicaciones de preparación\n";
        $mensaje .= "• Traer los resultados en su próxima cita\n\n";
        $mensaje .= "Para más información, contacte al consultorio.\n\n";
        $mensaje .= "Su salud es nuestra prioridad 💙";
        
        return $this->sendMessage($telefono, $mensaje);
    }
    
    /**
     * Registrar mensaje enviado en base de datos
     */
    public function registrarMensaje($telefono, $mensaje, $tipo, $paciente_id = null) {
        // Crear tabla de logs si no existe
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS WHATSAPP_LOGS (
                ID INT AUTO_INCREMENT PRIMARY KEY,
                PACIENTE_ID INT,
                TELEFONO VARCHAR(20),
                MENSAJE TEXT,
                TIPO VARCHAR(50),
                FECHA_ENVIO DATETIME DEFAULT CURRENT_TIMESTAMP,
                ESTADO VARCHAR(20) DEFAULT 'ENVIADO',
                RESPUESTA TEXT,
                INDEX idx_paciente (PACIENTE_ID),
                INDEX idx_fecha (FECHA_ENVIO),
                INDEX idx_tipo (TIPO)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $stmt = $this->pdo->prepare("
            INSERT INTO WHATSAPP_LOGS (PACIENTE_ID, TELEFONO, MENSAJE, TIPO)
            VALUES (?, ?, ?, ?)
        ");
        
        return $stmt->execute([$paciente_id, $telefono, $mensaje, $tipo]);
    }
}

// Inicializar el manager
$whatsapp = new WhatsAppManager($pdo);

// Procesar acciones
$mensaje_resultado = '';
$error_mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    
    switch ($accion) {
        case 'enviar_confirmaciones_hoy':
            $citas = $whatsapp->getCitasHoy();
            $enviados = 0;
            $errores = 0;
            
            foreach ($citas as $cita) {
                $resultado = $whatsapp->enviarConfirmacionCita($cita);
                if ($resultado['success']) {
                    $enviados++;
                    $whatsapp->registrarMensaje(
                        $cita['CELULAR'] ?: $cita['TELEFONO'],
                        'Confirmación de cita',
                        'CONFIRMACION_CITA',
                        $cita['CLAVEPAC']
                    );
                } else {
                    $errores++;
                }
            }
            
            $mensaje_resultado = "✅ Confirmaciones enviadas: {$enviados} | ❌ Errores: {$errores}";
            break;
            
        case 'enviar_recordatorios_manana':
            $citas = $whatsapp->getCitasManana();
            $enviados = 0;
            $errores = 0;
            
            foreach ($citas as $cita) {
                $resultado = $whatsapp->enviarRecordatorioCita($cita);
                if ($resultado['success']) {
                    $enviados++;
                    $whatsapp->registrarMensaje(
                        $cita['CELULAR'] ?: $cita['TELEFONO'],
                        'Recordatorio de cita',
                        'RECORDATORIO_CITA',
                        $cita['CLAVEPAC']
                    );
                } else {
                    $errores++;
                }
            }
            
            $mensaje_resultado = "✅ Recordatorios enviados: {$enviados} | ❌ Errores: {$errores}";
            break;
            
        case 'enviar_recordatorios_analisis':
            $pacientes = $whatsapp->getPacientesAnalisisPendientes();
            $enviados = 0;
            $errores = 0;
            
            foreach ($pacientes as $paciente) {
                $resultado = $whatsapp->enviarRecordatorioAnalisis($paciente);
                if ($resultado['success']) {
                    $enviados++;
                    $whatsapp->registrarMensaje(
                        $paciente['CELULAR'] ?: $paciente['TELEFONO'],
                        'Recordatorio de análisis',
                        'RECORDATORIO_ANALISIS',
                        null
                    );
                } else {
                    $errores++;
                }
            }
            
            $mensaje_resultado = "✅ Recordatorios de análisis enviados: {$enviados} | ❌ Errores: {$errores}";
            break;
            
        case 'mensaje_personalizado':
            $telefono = $_POST['telefono'] ?? '';
            $mensaje = $_POST['mensaje'] ?? '';
            
            if ($telefono && $mensaje) {
                $resultado = $whatsapp->sendMessage($telefono, $mensaje);
                if ($resultado['success']) {
                    $mensaje_resultado = "✅ Mensaje enviado correctamente";
                    $whatsapp->registrarMensaje($telefono, $mensaje, 'MENSAJE_PERSONALIZADO');
                } else {
                    $error_mensaje = "❌ Error al enviar mensaje: " . json_encode($resultado['response']);
                }
            } else {
                $error_mensaje = "❌ Teléfono y mensaje son obligatorios";
            }
            break;
    }
}

// Obtener estadísticas
$citas_hoy = $whatsapp->getCitasHoy();
$citas_manana = $whatsapp->getCitasManana();
$pacientes_analisis = $whatsapp->getPacientesAnalisisPendientes();

// Obtener logs recientes
$stmt = $pdo->query("
    SELECT * FROM WHATSAPP_LOGS
    ORDER BY FECHA_ENVIO DESC
    LIMIT 20
");
$logs_recientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de WhatsApp - Consultorio Médico</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --whatsapp-green: #25D366;
            --whatsapp-dark: #128C7E;
            --primary-color: #0d6efd;
            --success-color: #198754;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-bg: #f8f9fa;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            line-height: 1.6;
        }

        .whatsapp-header {
            background: linear-gradient(135deg, var(--whatsapp-green), var(--whatsapp-dark));
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .whatsapp-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease;
        }

        .whatsapp-card:hover {
            transform: translateY(-2px);
        }

        .stat-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            border-left: 4px solid var(--whatsapp-green);
        }

        .btn-whatsapp {
            background: var(--whatsapp-green);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-whatsapp:hover {
            background: var(--whatsapp-dark);
            color: white;
            transform: translateY(-1px);
        }

        .message-preview {
            background: #e7f3ff;
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-line;
        }

        .log-item {
            border-bottom: 1px solid #eee;
            padding: 0.75rem 0;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .badge-tipo {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .config-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="whatsapp-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="bi bi-whatsapp"></i>
                        Gestión de WhatsApp
                    </h1>
                    <p class="mb-0 opacity-75">Sistema de mensajería para citas y recordatorios médicos</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="../index.php" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i>
                        Volver al Panel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Mensajes de resultado -->
        <?php if ($mensaje_resultado): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill"></i>
                <?= htmlspecialchars($mensaje_resultado) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_mensaje): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <?= htmlspecialchars($error_mensaje) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Configuración -->
        <div class="config-section">
            <h5><i class="bi bi-gear-fill"></i> Configuración Importante</h5>
            <p class="mb-2">
                <strong>⚠️ Antes de usar este sistema:</strong>
            </p>
            <ol class="mb-0">
                <li>Configure su token de WhatsApp Business API en la línea 29 del código</li>
                <li>Configure su Phone Number ID en la línea 30</li>
                <li>Asegúrese de tener una cuenta de WhatsApp Business API activa</li>
                <li>Verifique que los números de teléfono estén en formato correcto</li>
            </ol>
        </div>

        <!-- Estadísticas -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card whatsapp-card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                        <h3 class="mt-2"><?= count($citas_hoy) ?></h3>
                        <p class="text-muted mb-0">Citas Hoy</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card whatsapp-card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                        <h3 class="mt-2"><?= count($citas_manana) ?></h3>
                        <p class="text-muted mb-0">Citas Mañana</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card whatsapp-card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-clipboard-data text-info" style="font-size: 2rem;"></i>
                        <h3 class="mt-2"><?= count($pacientes_analisis) ?></h3>
                        <p class="text-muted mb-0">Análisis Pendientes</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones Rápidas -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card whatsapp-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-send-check"></i>
                            Acciones Automáticas
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="d-grid gap-3">
                            <button type="submit" name="accion" value="enviar_confirmaciones_hoy" class="btn btn-whatsapp">
                                <i class="bi bi-calendar-check"></i>
                                Enviar Confirmaciones de Hoy (<?= count($citas_hoy) ?>)
                            </button>

                            <button type="submit" name="accion" value="enviar_recordatorios_manana" class="btn btn-whatsapp">
                                <i class="bi bi-clock"></i>
                                Enviar Recordatorios de Mañana (<?= count($citas_manana) ?>)
                            </button>

                            <button type="submit" name="accion" value="enviar_recordatorios_analisis" class="btn btn-whatsapp">
                                <i class="bi bi-clipboard-data"></i>
                                Recordatorios de Análisis (<?= count($pacientes_analisis) ?>)
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card whatsapp-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-chat-text"></i>
                            Mensaje Personalizado
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="telefono" class="form-label">Número de Teléfono</label>
                                <input type="tel" class="form-control" id="telefono" name="telefono"
                                       placeholder="Ej: 8091234567" required>
                                <div class="form-text">
                                    <i class="bi bi-info-circle"></i>
                                    Formato: 10 dígitos sin espacios ni guiones
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="mensaje" class="form-label">Mensaje</label>
                                <textarea class="form-control" id="mensaje" name="mensaje" rows="4"
                                          placeholder="Escriba su mensaje aquí..." required></textarea>
                                <div class="form-text">
                                    <i class="bi bi-emoji-smile"></i>
                                    Puede usar emojis y saltos de línea
                                </div>
                            </div>

                            <button type="submit" name="accion" value="mensaje_personalizado" class="btn btn-primary w-100">
                                <i class="bi bi-send"></i>
                                Enviar Mensaje
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vista Previa de Mensajes -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card whatsapp-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-eye"></i>
                            Vista Previa de Mensajes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>Confirmación de Cita</h6>
                                <div class="message-preview">🏥 *CONFIRMACIÓN DE CITA MÉDICA*

Estimado/a *Juan Pérez*,

Su cita médica ha sido confirmada:
📅 *Fecha:* 15/12/2024
🕐 *Hora:* 2:00 PM
🏥 *Consultorio:* Principal

📋 *Recomendaciones:*
• Llegar 15 minutos antes
• Traer documento de identidad
• Traer carnet del seguro (si aplica)

Para reprogramar o cancelar, contacte al consultorio.

¡Esperamos verle pronto! 👩‍⚕️👨‍⚕️</div>
                            </div>

                            <div class="col-md-4">
                                <h6>Recordatorio de Cita</h6>
                                <div class="message-preview">⏰ *RECORDATORIO DE CITA MÉDICA*

Estimado/a *María García*,

Le recordamos su cita médica para mañana:
📅 *Fecha:* 16/12/2024
🕐 *Hora:* 10:30 AM
🏥 *Consultorio:* Principal

📝 *No olvide traer:*
• Documento de identidad
• Carnet del seguro
• Estudios médicos previos

Si no puede asistir, por favor contacte al consultorio.

¡Nos vemos mañana! 🩺</div>
                            </div>

                            <div class="col-md-4">
                                <h6>Recordatorio de Análisis</h6>
                                <div class="message-preview">🔬 *RECORDATORIO DE ANÁLISIS MÉDICOS*

Estimado/a *Carlos López*,

Le recordamos que tiene análisis médicos pendientes de su última consulta.

📋 *Recomendaciones:*
• Realizar los estudios lo antes posible
• Seguir las indicaciones de preparación
• Traer los resultados en su próxima cita

Para más información, contacte al consultorio.

Su salud es nuestra prioridad 💙</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Listas de Citas -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card whatsapp-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-day"></i>
                            Citas de Hoy (<?= count($citas_hoy) ?>)
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <?php if (empty($citas_hoy)): ?>
                            <p class="text-muted text-center">
                                <i class="bi bi-calendar-x"></i>
                                No hay citas programadas para hoy
                            </p>
                        <?php else: ?>
                            <?php foreach ($citas_hoy as $cita): ?>
                                <div class="log-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong><?= htmlspecialchars(trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS'])) ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <i class="bi bi-clock"></i> <?= date('g:i A', strtotime($cita['HORACON'])) ?>
                                                <i class="bi bi-telephone ms-2"></i> <?= htmlspecialchars($cita['CELULAR'] ?: $cita['TELEFONO'] ?: 'Sin teléfono') ?>
                                            </small>
                                        </div>
                                        <span class="badge bg-success badge-tipo">Hoy</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card whatsapp-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-plus"></i>
                            Citas de Mañana (<?= count($citas_manana) ?>)
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <?php if (empty($citas_manana)): ?>
                            <p class="text-muted text-center">
                                <i class="bi bi-calendar-x"></i>
                                No hay citas programadas para mañana
                            </p>
                        <?php else: ?>
                            <?php foreach ($citas_manana as $cita): ?>
                                <div class="log-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong><?= htmlspecialchars(trim($cita['NOMBRES'] . ' ' . $cita['APELLIDOS'])) ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <i class="bi bi-clock"></i> <?= date('g:i A', strtotime($cita['HORACON'])) ?>
                                                <i class="bi bi-telephone ms-2"></i> <?= htmlspecialchars($cita['CELULAR'] ?: $cita['TELEFONO'] ?: 'Sin teléfono') ?>
                                            </small>
                                        </div>
                                        <span class="badge bg-warning badge-tipo">Mañana</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historial de Mensajes -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card whatsapp-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history"></i>
                            Historial de Mensajes Recientes
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                        <?php if (empty($logs_recientes)): ?>
                            <p class="text-muted text-center">
                                <i class="bi bi-chat-x"></i>
                                No hay mensajes enviados aún
                            </p>
                        <?php else: ?>
                            <?php foreach ($logs_recientes as $log): ?>
                                <div class="log-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-1">
                                                <i class="bi bi-telephone me-2"></i>
                                                <strong><?= htmlspecialchars($log['TELEFONO']) ?></strong>
                                                <span class="badge bg-<?=
                                                    $log['TIPO'] == 'CONFIRMACION_CITA' ? 'success' :
                                                    ($log['TIPO'] == 'RECORDATORIO_CITA' ? 'warning' :
                                                    ($log['TIPO'] == 'RECORDATORIO_ANALISIS' ? 'info' : 'primary'))
                                                ?> badge-tipo ms-2">
                                                    <?= htmlspecialchars($log['TIPO']) ?>
                                                </span>
                                            </div>
                                            <small class="text-muted">
                                                <i class="bi bi-clock"></i>
                                                <?= date('d/m/Y g:i A', strtotime($log['FECHA_ENVIO'])) ?>
                                            </small>
                                            <div class="mt-1">
                                                <small class="text-truncate d-block" style="max-width: 300px;">
                                                    <?= htmlspecialchars(substr($log['MENSAJE'], 0, 100)) ?>
                                                    <?= strlen($log['MENSAJE']) > 100 ? '...' : '' ?>
                                                </small>
                                            </div>
                                        </div>
                                        <span class="badge bg-<?= $log['ESTADO'] == 'ENVIADO' ? 'success' : 'danger' ?>">
                                            <?= htmlspecialchars($log['ESTADO']) ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instrucciones de Configuración -->
        <div class="row">
            <div class="col-12">
                <div class="card whatsapp-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i>
                            Instrucciones de Configuración
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-1-circle-fill text-primary"></i> Configurar WhatsApp Business API</h6>
                                <ol>
                                    <li>Crear una cuenta en <a href="https://business.whatsapp.com/" target="_blank">WhatsApp Business</a></li>
                                    <li>Obtener acceso a la API de WhatsApp Business</li>
                                    <li>Configurar webhook para recibir mensajes</li>
                                    <li>Obtener el token de acceso y Phone Number ID</li>
                                </ol>

                                <h6><i class="bi bi-2-circle-fill text-success"></i> Configurar el Sistema</h6>
                                <ol>
                                    <li>Editar las líneas 29-30 del archivo PHP</li>
                                    <li>Reemplazar "TU_TOKEN_DE_ACCESO" con su token real</li>
                                    <li>Reemplazar "TU_PHONE_NUMBER_ID" con su ID real</li>
                                    <li>Probar con un mensaje personalizado</li>
                                </ol>
                            </div>

                            <div class="col-md-6">
                                <h6><i class="bi bi-3-circle-fill text-warning"></i> Alternativas de API</h6>
                                <ul>
                                    <li><strong>Twilio:</strong> Fácil de configurar, pago por mensaje</li>
                                    <li><strong>Meta WhatsApp Business:</strong> Oficial, requiere aprobación</li>
                                    <li><strong>WhatsApp Business API Cloud:</strong> Gratuito hasta cierto límite</li>
                                    <li><strong>Proveedores locales:</strong> Consulte opciones en su país</li>
                                </ul>

                                <h6><i class="bi bi-4-circle-fill text-info"></i> Consideraciones Importantes</h6>
                                <ul>
                                    <li>Verificar números de teléfono válidos</li>
                                    <li>Respetar horarios de envío (8 AM - 8 PM)</li>
                                    <li>Obtener consentimiento de pacientes</li>
                                    <li>Cumplir con regulaciones de privacidad</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Formatear número de teléfono mientras se escribe
        document.getElementById('telefono').addEventListener('input', function(e) {
            // Remover caracteres no numéricos
            let value = e.target.value.replace(/[^0-9]/g, '');

            // Limitar a 10 dígitos
            if (value.length > 10) {
                value = value.slice(0, 10);
            }

            e.target.value = value;
        });

        // Confirmación antes de enviar mensajes masivos
        document.querySelectorAll('button[name="accion"]').forEach(button => {
            if (button.value !== 'mensaje_personalizado') {
                button.addEventListener('click', function(e) {
                    const action = this.value;
                    let message = '';

                    switch(action) {
                        case 'enviar_confirmaciones_hoy':
                            message = '¿Está seguro de enviar confirmaciones a todas las citas de hoy?';
                            break;
                        case 'enviar_recordatorios_manana':
                            message = '¿Está seguro de enviar recordatorios a todas las citas de mañana?';
                            break;
                        case 'enviar_recordatorios_analisis':
                            message = '¿Está seguro de enviar recordatorios de análisis a todos los pacientes?';
                            break;
                    }

                    if (message && !confirm(message)) {
                        e.preventDefault();
                    }
                });
            }
        });

        // Auto-refresh cada 5 minutos para actualizar las citas
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutos
    </script>
</body>
</html>
