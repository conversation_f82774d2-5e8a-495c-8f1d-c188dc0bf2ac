<?php
/**
 * WhatsApp Manager - MarcSoftware Solutions
 * Gestión de mensajes de WhatsApp para facturas y comunicación con clientes
 * 
 * <AUTHOR> Solutions
 * @version 1.0
 * @date 2024
 */

class WhatsAppManager {
    
    private $default_country_code = '1809'; // República Dominicana
    
    /**
     * Limpiar y formatear número de teléfono
     */
    public function formatPhoneNumber($phone, $country_code = null) {
        if (empty($phone)) {
            return '';
        }
        
        // Remover todos los caracteres no numéricos
        $clean_phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Usar código de país por defecto si no se proporciona
        if (!$country_code) {
            $country_code = $this->default_country_code;
        }
        
        // Formatear según la longitud
        if (strlen($clean_phone) === 10) {
            // Número local de 10 dígitos
            return $country_code . $clean_phone;
        } elseif (strlen($clean_phone) === 7) {
            // Número local de 7 dígitos
            return $country_code . $clean_phone;
        } elseif (strlen($clean_phone) > 10) {
            // Ya incluye código de país
            return $clean_phone;
        }
        
        return $clean_phone;
    }
    
    /**
     * Generar mensaje de factura para WhatsApp
     */
    public function generateInvoiceMessage($factura_data) {
        $message = "🧾 *FACTURA GENERADA*\n\n";
        $message .= "📄 *Número:* " . $factura_data['numero'] . "\n";
        $message .= "👤 *Cliente:* " . $factura_data['cliente'] . "\n";
        $message .= "💰 *Monto:* " . $factura_data['moneda'] . " " . number_format($factura_data['precio'], 2) . "\n";
        $message .= "📅 *Fecha de Emisión:* " . date('d/m/Y', strtotime($factura_data['fecha_emision'])) . "\n";
        $message .= "📅 *Fecha de Vencimiento:* " . date('d/m/Y', strtotime($factura_data['fecha_vencimiento'])) . "\n";
        $message .= "💳 *Método de Pago:* " . $factura_data['modo_pago'] . "\n\n";
        
        // Agregar datos bancarios si es transferencia
        if ($factura_data['modo_pago'] === 'Transferencia' && !empty($factura_data['banco'])) {
            $message .= "🏦 *DATOS BANCARIOS:*\n";
            $message .= "• *Banco:* " . $factura_data['banco'] . "\n";
            $message .= "• *Cuenta:* " . $factura_data['cuenta'] . "\n";
            $message .= "• *Beneficiario:* " . $factura_data['beneficiario'] . "\n\n";
            $message .= "📱 *Importante:* Envíe el comprobante de transferencia para confirmar el pago.\n\n";
        }
        
        $message .= "✨ Gracias por elegir *MarcSoftware Solutions*\n";
        $message .= "🚀 Su socio en desarrollo de software\n\n";
        
        if (!empty($factura_data['pdf_url'])) {
            $message .= "📎 Descargue su factura: " . $factura_data['pdf_url'];
        }
        
        return $message;
    }
    
    /**
     * Generar mensaje de recordatorio de pago
     */
    public function generatePaymentReminderMessage($factura_data, $days_overdue = 0) {
        if ($days_overdue > 0) {
            $message = "⚠️ *RECORDATORIO DE PAGO VENCIDO*\n\n";
            $message .= "Su factura tiene *{$days_overdue} días de vencida*\n\n";
        } else {
            $message = "🔔 *RECORDATORIO DE PAGO*\n\n";
        }
        
        $message .= "📄 *Factura:* " . $factura_data['numero'] . "\n";
        $message .= "👤 *Cliente:* " . $factura_data['cliente'] . "\n";
        $message .= "💰 *Monto Pendiente:* " . $factura_data['moneda'] . " " . number_format($factura_data['precio'], 2) . "\n";
        $message .= "📅 *Fecha de Vencimiento:* " . date('d/m/Y', strtotime($factura_data['fecha_vencimiento'])) . "\n\n";
        
        if ($factura_data['modo_pago'] === 'Transferencia' && !empty($factura_data['banco'])) {
            $message .= "🏦 *DATOS PARA PAGO:*\n";
            $message .= "• *Banco:* " . $factura_data['banco'] . "\n";
            $message .= "• *Cuenta:* " . $factura_data['cuenta'] . "\n";
            $message .= "• *Beneficiario:* " . $factura_data['beneficiario'] . "\n\n";
        }
        
        $message .= "📞 Para cualquier consulta, contáctenos.\n";
        $message .= "Gracias por su atención.\n\n";
        $message .= "*MarcSoftware Solutions*";
        
        return $message;
    }
    
    /**
     * Generar URL de WhatsApp con mensaje
     */
    public function generateWhatsAppURL($phone, $message) {
        $formatted_phone = $this->formatPhoneNumber($phone);
        $encoded_message = urlencode($message);
        
        return "https://wa.me/{$formatted_phone}?text={$encoded_message}";
    }
    
    /**
     * Generar URL de WhatsApp Web (sin número específico)
     */
    public function generateWhatsAppWebURL($message) {
        $encoded_message = urlencode($message);
        return "https://web.whatsapp.com/send?text={$encoded_message}";
    }
    
    /**
     * Validar si un número de teléfono es válido para WhatsApp
     */
    public function isValidWhatsAppNumber($phone) {
        $formatted = $this->formatPhoneNumber($phone);
        return !empty($formatted) && strlen($formatted) >= 10;
    }
    
    /**
     * Generar mensaje de confirmación de pago
     */
    public function generatePaymentConfirmationMessage($factura_data) {
        $message = "✅ *PAGO CONFIRMADO*\n\n";
        $message .= "¡Gracias por su pago!\n\n";
        $message .= "📄 *Factura:* " . $factura_data['numero'] . "\n";
        $message .= "💰 *Monto Pagado:* " . $factura_data['moneda'] . " " . number_format($factura_data['precio'], 2) . "\n";
        $message .= "📅 *Fecha de Pago:* " . date('d/m/Y') . "\n\n";
        $message .= "Su factura ha sido marcada como *PAGADA* en nuestro sistema.\n\n";
        $message .= "✨ Gracias por elegir *MarcSoftware Solutions*\n";
        $message .= "🚀 Esperamos seguir trabajando juntos";
        
        return $message;
    }
    
    /**
     * Generar mensaje de bienvenida para nuevos clientes
     */
    public function generateWelcomeMessage($cliente_nombre) {
        $message = "🎉 *¡BIENVENIDO A MARCSOFTWARE SOLUTIONS!*\n\n";
        $message .= "Hola *{$cliente_nombre}*,\n\n";
        $message .= "Gracias por confiar en nosotros para sus proyectos de desarrollo de software.\n\n";
        $message .= "🚀 *Nuestros Servicios:*\n";
        $message .= "• Desarrollo de aplicaciones web\n";
        $message .= "• Sistemas de gestión médica\n";
        $message .= "• Consultoría tecnológica\n";
        $message .= "• Soporte técnico especializado\n\n";
        $message .= "📞 *Contacto:*\n";
        $message .= "• WhatsApp: Este mismo número\n";
        $message .= "• Email: <EMAIL>\n\n";
        $message .= "¡Estamos aquí para ayudarle a hacer realidad sus ideas! 💡";

        return $message;
    }

    /**
     * Generar mensaje de recordatorio de cita médica
     */
    public function generateAppointmentReminderMessage($cita_data) {
        $dias = $cita_data['dias_hasta_cita'];
        $fecha_cita = date('d/m/Y', strtotime($cita_data['fecha']));
        $hora_cita = date('h:i A', strtotime($cita_data['hora']));

        if ($dias == 0) {
            $cuando = "HOY";
            $emoji = "🚨";
        } elseif ($dias == 1) {
            $cuando = "MAÑANA";
            $emoji = "⏰";
        } else {
            $cuando = "en {$dias} días";
            $emoji = "📅";
        }

        $message = "{$emoji} *RECORDATORIO DE CITA MÉDICA*\n\n";
        $message .= "Estimado/a *{$cita_data['paciente']}*,\n\n";
        $message .= "Le recordamos que tiene una cita médica programada para *{$cuando}*:\n\n";
        $message .= "📅 *Fecha:* {$fecha_cita}\n";
        $message .= "🕐 *Hora:* {$hora_cita}\n";

        if (!empty($cita_data['consultorio'])) {
            $message .= "🏥 *Consultorio:* {$cita_data['consultorio']}\n";
        }

        if (!empty($cita_data['doctor'])) {
            $message .= "👨‍⚕️ *Doctor:* {$cita_data['doctor']}\n";
        }

        if (!empty($cita_data['observaciones'])) {
            $message .= "📝 *Observaciones:* {$cita_data['observaciones']}\n";
        }

        $message .= "\n📋 *Recomendaciones:*\n";
        $message .= "• Llegue 15 minutos antes de su cita\n";
        $message .= "• Traiga su cédula de identidad\n";
        $message .= "• Traiga su tarjeta de seguro (si aplica)\n";
        $message .= "• Si tiene estudios previos, tráigalos\n\n";

        if ($dias <= 1) {
            $message .= "⚠️ *IMPORTANTE:* Si no puede asistir, por favor comuníquese con nosotros lo antes posible.\n\n";
        } else {
            $message .= "Si necesita reprogramar su cita, contáctenos con anticipación.\n\n";
        }

        $message .= "📞 *Contacto:* " . (defined('WHATSAPP_SOPORTE') ? WHATSAPP_SOPORTE : '18095551234') . "\n";
        $message .= "🏥 *Consultorio Médico*\n";
        $message .= "Cuidando su salud con profesionalismo ❤️";

        return $message;
    }

    /**
     * Generar mensaje de confirmación de cita
     */
    public function generateAppointmentConfirmationMessage($cita_data) {
        $fecha_cita = date('d/m/Y', strtotime($cita_data['fecha']));
        $hora_cita = date('h:i A', strtotime($cita_data['hora']));

        $message = "✅ *CITA CONFIRMADA*\n\n";
        $message .= "Estimado/a *{$cita_data['paciente']}*,\n\n";
        $message .= "Su cita médica ha sido confirmada:\n\n";
        $message .= "📅 *Fecha:* {$fecha_cita}\n";
        $message .= "🕐 *Hora:* {$hora_cita}\n";

        if (!empty($cita_data['consultorio'])) {
            $message .= "🏥 *Consultorio:* {$cita_data['consultorio']}\n";
        }

        if (!empty($cita_data['doctor'])) {
            $message .= "👨‍⚕️ *Doctor:* {$cita_data['doctor']}\n";
        }

        $message .= "\n📍 *Dirección del Consultorio:*\n";
        $message .= "[Agregar dirección aquí]\n\n";

        $message .= "📋 *Recordatorios:*\n";
        $message .= "• Llegue 15 minutos antes\n";
        $message .= "• Traiga documentos de identidad\n";
        $message .= "• Traiga tarjeta de seguro\n\n";

        $message .= "¡Esperamos verle pronto! 🏥";

        return $message;
    }

    /**
     * Generar mensaje de cancelación de cita
     */
    public function generateAppointmentCancellationMessage($cita_data) {
        $fecha_cita = date('d/m/Y', strtotime($cita_data['fecha']));
        $hora_cita = date('h:i A', strtotime($cita_data['hora']));

        $message = "❌ *CITA CANCELADA*\n\n";
        $message .= "Estimado/a *{$cita_data['paciente']}*,\n\n";
        $message .= "Le informamos que su cita médica ha sido cancelada:\n\n";
        $message .= "📅 *Fecha:* {$fecha_cita}\n";
        $message .= "🕐 *Hora:* {$hora_cita}\n\n";

        if (!empty($cita_data['motivo_cancelacion'])) {
            $message .= "📝 *Motivo:* {$cita_data['motivo_cancelacion']}\n\n";
        }

        $message .= "Para reprogramar su cita, por favor contáctenos:\n";
        $message .= "📞 " . (defined('WHATSAPP_SOPORTE') ? WHATSAPP_SOPORTE : '18095551234') . "\n\n";

        $message .= "Lamentamos cualquier inconveniente.\n";
        $message .= "🏥 *Consultorio Médico*";

        return $message;
    }
    
    /**
     * Obtener plantillas de mensajes predefinidas
     */
    public function getMessageTemplates() {
        return [
            'invoice' => 'Mensaje de factura generada',
            'reminder' => 'Recordatorio de pago',
            'overdue' => 'Pago vencido',
            'confirmation' => 'Confirmación de pago',
            'welcome' => 'Mensaje de bienvenida',
            'support' => 'Mensaje de soporte técnico'
        ];
    }
    
    /**
     * Generar botones HTML para WhatsApp
     */
    public function generateWhatsAppButtons($phone, $message, $show_copy = true) {
        $html = '<div class="whatsapp-buttons">';
        
        if ($this->isValidWhatsAppNumber($phone)) {
            $url = $this->generateWhatsAppURL($phone, $message);
            $formatted_phone = $this->formatPhoneNumber($phone);
            
            $html .= '<a href="' . htmlspecialchars($url) . '" target="_blank" class="btn btn-success me-2">';
            $html .= '<i class="bi bi-whatsapp"></i> Enviar WhatsApp';
            $html .= '</a>';
            $html .= '<small class="text-muted d-block">Teléfono: +' . $formatted_phone . '</small>';
        } else {
            $web_url = $this->generateWhatsAppWebURL($message);
            $html .= '<a href="' . htmlspecialchars($web_url) . '" target="_blank" class="btn btn-success me-2">';
            $html .= '<i class="bi bi-whatsapp"></i> WhatsApp Web';
            $html .= '</a>';
        }
        
        if ($show_copy) {
            $html .= '<button type="button" class="btn btn-outline-success" onclick="copyToClipboard(\'' . htmlspecialchars($message, ENT_QUOTES) . '\')">';
            $html .= '<i class="bi bi-clipboard"></i> Copiar Mensaje';
            $html .= '</button>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}

// Función JavaScript para copiar al portapapeles
function getClipboardScript() {
    return '
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Mostrar notificación de éxito
                showNotification("✅ Mensaje copiado al portapapeles", "success");
            }).catch(function(err) {
                // Fallback para navegadores más antiguos
                const textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand("copy");
                document.body.removeChild(textArea);
                showNotification("✅ Mensaje copiado al portapapeles", "success");
            });
        }
        
        function showNotification(message, type = "info") {
            // Crear notificación toast
            const toast = document.createElement("div");
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            // Auto-remover después de 3 segundos
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
    </script>';
}
?>
