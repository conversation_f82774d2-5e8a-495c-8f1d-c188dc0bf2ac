<?php
// --- LÍNEAS TEMPORALES PARA DEPURACIÓN: ELIMINAR EN PRODUCCIÓN DESPUÉS DE SOLUCIONAR EL PROBLEMA ---
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

require_once dirname(__DIR__) . '/config/database.php';


$masterPdo = getMasterPdo();

use Dompdf\Dompdf;
use Dompdf\Options;

// 1. Obtener el ID de la factura y el subdominio desde la URL
$id_factura = $_GET['id_factura'] ?? null;
$subdominio_seleccionado = $_GET['subdominio'] ?? null;

if (!$id_factura || !$subdominio_seleccionado) {
    die("Error: ID de factura o subdominio no especificado en la URL. No se puede generar el PDF.");
}

$pdo = null; // Variable para la conexión al tenant específico
$empresa = null; // Variable para los datos de la EMPRESA del tenant

try {
    // Recuperar la cadena de conexión del tenant de la base de datos maestra
    $stmt_master = $masterPdo->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio = ? AND Estado = 'Activo'");
    $stmt_master->execute([$subdominio_seleccionado]);
    $cfg = $stmt_master->fetch(PDO::FETCH_ASSOC);

    if ($cfg && !empty($cfg['CadenaConexionDB'])) {
        // Parsear la cadena de conexión
        preg_match('/host=([^;]+)/', $cfg['CadenaConexionDB'], $matches_host); $host = $matches_host[1] ?? '';
        preg_match('/dbname=([^;]+)/', $cfg['CadenaConexionDB'], $matches_dbname); $dbname = $matches_dbname[1] ?? '';
        preg_match('/user=([^;]+)/', $cfg['CadenaConexionDB'], $matches_user); $user = $matches_user[1] ?? '';
        preg_match('/password=([^;]+)/', $cfg['CadenaConexionDB'], $matches_pass); $password = $matches_pass[1] ?? '';

        if ($host && $dbname && $user !== '') {
            $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
            $pdo = new PDO($dsn, $user, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);

            // 2. Obtener los datos completos de la factura y del cliente del tenant
            $stmt = $pdo->prepare("SELECT
                                        fs.*,
                                        cs.NOMBRE_COMPLETO AS CLIENTE_NOMBRE_COMPLETO,
                                        cs.RNC AS CLIENTE_RNC,
                                        cs.TELEFONO AS CLIENTE_TELEFONO,
                                        cs.EMAIL AS CLIENTE_EMAIL,
                                        cs.DIRECCION AS CLIENTE_DIRECCION,
                                        cs.CIUDAD AS CLIENTE_CIUDAD,
                                        cs.PROVINCIA AS CLIENTE_PROVINCIA,
                                        cs.ESPECIALIDAD AS CLIENTE_ESPECIALIDAD,
                                        cs.SUBDOMINIO AS CLIENTE_SUBDOMINIO
                                    FROM
                                        FACTURAS_SOFTWARE fs
                                    JOIN
                                        CLIENTES_SOFTWARE cs ON fs.CLIENTE_CLAVE = cs.CLAVE
                                    WHERE
                                        fs.CLAVE = ?");
            $stmt->execute([$id_factura]);
            $factura = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$factura) {
                die("Error: Factura con ID " . htmlspecialchars($id_factura) . " no encontrada para el consultorio '{$subdominio_seleccionado}'.");
            }

            // 3. Obtener los datos del "consultorio" (MarcSoftware Solutions) desde la tabla EMPRESA del tenant
            $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
            $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

            // Si no hay datos de empresa, usar valores por defecto para evitar errores
            $empresa_nombre = $empresa['NOMBRE'] ?? 'MarcSoftware Solutions';
            $empresa_rnc = $empresa['RNC'] ?? 'N/A';
            $empresa_telefono = $empresa['TELEFONO'] ?? 'N/A';
            $empresa_email = $empresa['CORREOE'] ?? '<EMAIL>';
            $empresa_direccion = '';
            if (isset($empresa['CALLE'])) $empresa_direccion .= htmlspecialchars($empresa['CALLE']);
            if (isset($empresa['MUNICIPIO'])) $empresa_direccion .= ($empresa_direccion ? ', ' : '') . htmlspecialchars($empresa['MUNICIPIO']);
            if (isset($empresa['PROVINCIA'])) $empresa_direccion .= ($empresa_direccion ? ', ' : '') . htmlspecialchars($empresa['PROVINCIA']);


            // 4. Construir el HTML de la factura con diseño profesional mejorado
            $html = '
            <!DOCTYPE html>
            <html lang="es">
            <head>
                <meta charset="UTF-8">
                <title>Factura ' . htmlspecialchars($factura['NUMERO_FACTURA']) . ' - MarcSoftware Solutions</title>
                <style>
                    /* Reset y configuración base */
                    * { margin: 0; padding: 0; box-sizing: border-box; }
                    body {
                        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                        font-size: 11pt;
                        line-height: 1.5;
                        color: #2c3e50;
                        background: #ffffff;
                    }

                    .container {
                        width: 100%;
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 30px;
                        background: #ffffff;
                    }

                    /* Header con branding */
                    .header {
                        text-align: center;
                        margin-bottom: 40px;
                        padding: 20px 0;
                        border-bottom: 3px solid #3498db;
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    }

                    .header h1 {
                        font-size: 28pt;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 5px;
                        text-transform: uppercase;
                        letter-spacing: 2px;
                    }

                    .header .subtitle {
                        font-size: 12pt;
                        color: #7f8c8d;
                        font-weight: 300;
                        margin-bottom: 15px;
                    }

                    .header .company-name {
                        font-size: 16pt;
                        font-weight: 600;
                        color: #3498db;
                        margin-top: 10px;
                    }

                    /* Layout de información */
                    .info-section {
                        display: table;
                        width: 100%;
                        margin-bottom: 30px;
                    }

                    .info-left, .info-right {
                        display: table-cell;
                        width: 50%;
                        vertical-align: top;
                        padding: 20px;
                    }

                    .info-right {
                        text-align: right;
                    }

                    .info-box {
                        background: #f8f9fa;
                        border-left: 4px solid #3498db;
                        padding: 15px;
                        margin-bottom: 15px;
                    }

                    .info-box.client {
                        border-left-color: #27ae60;
                    }

                    .info-box.invoice {
                        border-left-color: #e74c3c;
                        background: #fff5f5;
                    }

                    .info-label {
                        font-weight: 600;
                        color: #34495e;
                        font-size: 10pt;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        margin-bottom: 5px;
                        display: block;
                    }

                    .info-value {
                        margin-bottom: 8px;
                        font-size: 11pt;
                        line-height: 1.4;
                    }

                    .info-value strong {
                        font-weight: 600;
                        color: #2c3e50;
                    }

                    /* Separadores */
                    .divider {
                        border: 0;
                        height: 2px;
                        background: linear-gradient(to right, #3498db, #2ecc71);
                        margin: 30px 0;
                    }

                    /* Tabla de servicios */
                    .services-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 30px 0;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        overflow: hidden;
                    }

                    .services-table th {
                        background: linear-gradient(135deg, #3498db, #2980b9);
                        color: white;
                        padding: 15px;
                        text-align: left;
                        font-weight: 600;
                        font-size: 11pt;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }

                    .services-table td {
                        padding: 15px;
                        border-bottom: 1px solid #ecf0f1;
                        vertical-align: top;
                        background: #ffffff;
                    }

                    .services-table tr:nth-child(even) td {
                        background: #f8f9fa;
                    }

                    .services-table td:last-child {
                        text-align: right;
                        font-weight: 600;
                        color: #27ae60;
                        font-size: 12pt;
                    }

                    /* Sección de totales */
                    .total-section {
                        text-align: right;
                        margin: 30px 0;
                    }

                    .total-box {
                        display: inline-block;
                        background: linear-gradient(135deg, #27ae60, #2ecc71);
                        color: white;
                        padding: 20px 30px;
                        border-radius: 8px;
                        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
                    }

                    .total-box .label {
                        font-size: 12pt;
                        font-weight: 300;
                        margin-bottom: 5px;
                        display: block;
                    }

                    .total-box .amount {
                        font-size: 20pt;
                        font-weight: 700;
                        display: block;
                    }

                    /* Notas y información adicional */
                    .notes-section {
                        margin: 30px 0;
                        padding: 20px;
                        background: #f8f9fa;
                        border-radius: 8px;
                        border-left: 4px solid #f39c12;
                    }

                    .notes-section h3 {
                        color: #34495e;
                        font-size: 12pt;
                        margin-bottom: 15px;
                        font-weight: 600;
                    }

                    .notes-section p {
                        margin-bottom: 10px;
                        line-height: 1.6;
                    }

                    .bank-info {
                        background: #e8f5e8;
                        border: 1px solid #27ae60;
                        border-radius: 6px;
                        padding: 15px;
                        margin: 15px 0;
                    }

                    .bank-info h4 {
                        color: #27ae60;
                        font-size: 11pt;
                        margin-bottom: 10px;
                        font-weight: 600;
                    }

                    /* Footer */
                    .footer {
                        text-align: center;
                        margin-top: 50px;
                        padding: 20px 0;
                        border-top: 2px solid #ecf0f1;
                        font-size: 10pt;
                        color: #7f8c8d;
                        background: #f8f9fa;
                    }

                    .footer .company-info {
                        font-weight: 600;
                        color: #3498db;
                        margin-bottom: 5px;
                    }

                    /* Utilidades */
                    .no-break { page-break-inside: avoid; }
                    .text-center { text-align: center; }
                    .text-right { text-align: right; }
                    .text-bold { font-weight: 600; }

                    /* Status badge */
                    .status-badge {
                        display: inline-block;
                        padding: 5px 12px;
                        border-radius: 20px;
                        font-size: 9pt;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }

                    .status-pendiente {
                        background: #fff3cd;
                        color: #856404;
                        border: 1px solid #ffeaa7;
                    }

                    .status-pagado {
                        background: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                    }

                    /* Configuración de página */
                    @page {
                        margin: 0.75in;
                        size: letter;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <!-- Header con branding -->
                    <div class="header no-break">
                        <h1>Factura Comercial</h1>
                        <div class="subtitle">Servicios de Desarrollo de Software</div>
                        <div class="company-name">MarcSoftware Solutions</div>
                    </div>

                    <!-- Información de empresa y factura -->
                    <div class="info-section no-break">
                        <div class="info-left">
                            <div class="info-box">
                                <span class="info-label">Facturado por:</span>
                                <div class="info-value">
                                    <strong>' . htmlspecialchars($empresa_nombre) . '</strong>
                                </div>
                                <div class="info-value">
                                    <strong>RNC:</strong> ' . htmlspecialchars($empresa_rnc) . '
                                </div>
                                <div class="info-value">
                                    <strong>Teléfono:</strong> ' . htmlspecialchars($empresa_telefono) . '
                                </div>
                                <div class="info-value">
                                    <strong>Email:</strong> ' . htmlspecialchars($empresa_email) . '
                                </div>
                                <div class="info-value">
                                    <strong>Dirección:</strong> ' . htmlspecialchars($empresa_direccion ?: 'No especificada') . '
                                </div>
                            </div>
                        </div>

                        <div class="info-right">
                            <div class="info-box invoice">
                                <span class="info-label">Detalles de Factura:</span>
                                <div class="info-value">
                                    <strong>Factura No.:</strong>
                                    <span style="font-size: 14pt; color: #e74c3c; font-weight: 700;">' . htmlspecialchars($factura['NUMERO_FACTURA']) . '</span>
                                </div>
                                <div class="info-value">
                                    <strong>Fecha de Emisión:</strong> ' . date('d/m/Y', strtotime($factura['FECHA_FACTURA'])) . '
                                </div>
                                <div class="info-value">
                                    <strong>Fecha de Vencimiento:</strong> ' . date('d/m/Y', strtotime($factura['FECHA_VENCIMIENTO'])) . '
                                </div>
                                <div class="info-value">
                                    <strong>Estado:</strong>
                                    <span class="status-badge status-' . strtolower($factura['ESTATUS']) . '">' . htmlspecialchars(strtoupper($factura['ESTATUS'])) . '</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <!-- Información del cliente -->
                    <div class="info-section no-break">
                        <div class="info-left">
                            <div class="info-box client">
                                <span class="info-label">Facturado a:</span>
                                <div class="info-value">
                                    <strong>' . htmlspecialchars($factura['CLIENTE_NOMBRE_COMPLETO']) . '</strong>
                                </div>
                                <div class="info-value">
                                    <strong>RNC:</strong> ' . htmlspecialchars($factura['CLIENTE_RNC'] ?: 'No especificado') . '
                                </div>
                                <div class="info-value">
                                    <strong>Teléfono:</strong> ' . htmlspecialchars($factura['CLIENTE_TELEFONO'] ?: 'No especificado') . '
                                </div>
                                <div class="info-value">
                                    <strong>Email:</strong> ' . htmlspecialchars($factura['CLIENTE_EMAIL'] ?: 'No especificado') . '
                                </div>
                                <div class="info-value">
                                    <strong>Dirección:</strong> ' . htmlspecialchars(trim($factura['CLIENTE_DIRECCION'] . ', ' . $factura['CLIENTE_CIUDAD'] . ', ' . $factura['CLIENTE_PROVINCIA'], ', ') ?: 'No especificada') . '
                                </div>
                                ' . (!empty($factura['CLIENTE_ESPECIALIDAD']) ? '<div class="info-value"><strong>Especialidad:</strong> ' . htmlspecialchars($factura['CLIENTE_ESPECIALIDAD']) . '</div>' : '') . '
                                ' . (!empty($factura['CLIENTE_SUBDOMINIO']) ? '<div class="info-value"><strong>Subdominio:</strong> ' . htmlspecialchars($factura['CLIENTE_SUBDOMINIO']) . '</div>' : '') . '
                            </div>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <!-- Tabla de servicios -->
                    <table class="services-table no-break">
                        <thead>
                            <tr>
                                <th style="width: 70%;">Descripción del Servicio</th>
                                <th style="width: 15%; text-align: center;">Cantidad</th>
                                <th style="width: 15%; text-align: right;">Monto Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div style="font-weight: 600; margin-bottom: 8px; color: #2c3e50;">
                                        Servicios de Desarrollo de Software
                                    </div>
                                    <div style="font-size: 10pt; line-height: 1.6; color: #5a6c7d;">
                                        ' . nl2br(htmlspecialchars($factura['CONCEPTO'])) . '
                                    </div>
                                </td>
                                <td style="text-align: center; font-weight: 600;">1</td>
                                <td style="text-align: right; font-weight: 700; color: #27ae60; font-size: 12pt;">
                                    ' . htmlspecialchars($factura['MONEDA']) . ' ' . number_format($factura['PRECIO'], 2) . '
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Sección de totales -->
                    <div class="total-section no-break">
                        <div class="total-box">
                            <span class="label">Total a Pagar:</span>
                            <span class="amount">' . htmlspecialchars($factura['MONEDA']) . ' ' . number_format($factura['PRECIO'], 2) . '</span>
                        </div>
                    </div>

                    <!-- Información de pago y notas -->
                    <div class="notes-section no-break">
                        <h3>Información de Pago</h3>
                        <p><strong>Método de Pago:</strong> ' . htmlspecialchars($factura['MODO_PAGO']) . '</p>';

                        if ($factura['MODO_PAGO'] === 'Transferencia' && !empty($factura['BANCO_DESTINO'])) {
                            $html .= '
                            <div class="bank-info">
                                <h4>📱 Datos Bancarios para Transferencia</h4>
                                <p><strong>🏦 Banco:</strong> ' . htmlspecialchars($factura['BANCO_DESTINO']) . '</p>
                                <p><strong>💳 Número de Cuenta:</strong> ' . htmlspecialchars($factura['CUENTA_DESTINO']) . '</p>
                                <p><strong>👤 Beneficiario:</strong> ' . htmlspecialchars($factura['BENEFICIARIO']) . '</p>
                                <p style="font-size: 10pt; color: #7f8c8d; margin-top: 10px;">
                                    <em>Por favor, envíe el comprobante de transferencia a ' . htmlspecialchars($empresa_email) . ' para confirmar el pago.</em>
                                </p>
                            </div>';
                        }

                    $html .= '
                        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 6px; border-left: 4px solid #3498db;">
                            <p style="margin: 0; font-size: 10pt; line-height: 1.6;">
                                <strong>📋 Términos y Condiciones:</strong><br>
                                • El pago debe realizarse antes de la fecha de vencimiento indicada.<br>
                                • Los servicios prestados están sujetos a los términos acordados en el contrato.<br>
                                • Para cualquier consulta, contacte a nuestro equipo de soporte.<br>
                                • Este documento es válido como comprobante fiscal.
                            </p>
                        </div>

                        <p style="text-align: center; margin-top: 20px; font-style: italic; color: #7f8c8d;">
                            ¡Gracias por confiar en MarcSoftware Solutions para sus proyectos de desarrollo!<br>
                            Este documento ha sido generado automáticamente y no requiere firma.
                        </p>
                    </div>

                    <!-- Footer -->
                    <div class="footer no-break">
                        <div class="company-info">MarcSoftware Solutions</div>
                        <div>Desarrollo de Software Profesional | Soluciones Tecnológicas Innovadoras</div>
                        <div style="margin-top: 8px;">
                            📞 ' . htmlspecialchars($empresa_telefono) . ' |
                            📧 ' . htmlspecialchars($empresa_email) . ' |
                            🌐 www.marcsoftware.com
                        </div>
                        <div style="margin-top: 8px; font-size: 9pt;">
                            Documento generado el ' . date('d/m/Y H:i:s') . ' | Factura ' . htmlspecialchars($factura['NUMERO_FACTURA']) . '
                        </div>
                    </div>
                </div>
            </body>
            </html>';

            // 5. Configurar Dompdf y generar el PDF
            $options = new Options();
            $options->set('isHtml5ParserEnabled', true);
            $options->set('isRemoteEnabled', true);
            $dompdf = new Dompdf($options);

            $dompdf->loadHtml($html);
            $dompdf->setPaper('letter', 'portrait');
            $dompdf->render();

            // Enviar el PDF al navegador
            $dompdf->stream("Factura_" . $factura['NUMERO_FACTURA'] . ".pdf", array("Attachment" => false));

        } else {
            die("Error: Datos de conexión incompletos o inválidos para el subdominio '{$subdominio_seleccionado}'.");
        }
    } else {
        die("Error: Configuración de conexión no encontrada o inactiva para el subdominio '{$subdominio_seleccionado}'.");
    }

} catch (\PDOException $e) {
    die("Error de base de datos al generar la factura para '{$subdominio_seleccionado}': " . htmlspecialchars($e->getMessage()));
} catch (Exception $e) {
    die("Error inesperado al generar la factura para '{$subdominio_seleccionado}': " . htmlspecialchars($e->getMessage()));
}